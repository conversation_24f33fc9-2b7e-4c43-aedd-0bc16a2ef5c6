import { useEffect, useRef, useState } from "react";
import {
  HubConnectionBuilder,
  LogLevel,
  HttpTransportType,
} from "@microsoft/signalr";
import {
  ArrowDownOutlined,
  ArrowLeftOutlined,
  ArrowUpOutlined,
  PhoneFilled,
  PhoneTwoTone,
} from "@ant-design/icons";
import { Drawer, Modal, Space, Tabs, Typography } from "antd";
import { setBackEndUrl } from "@/helpers/SetBackEndUrl";
import AutoDailerCallIndex from "@/apps/Admin/Pages/AutoDialer/Components/Call/AutoDailerCallIndex";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { useSearchParams } from "react-router-dom";
import PageTitle from "@/apps/Common/PageTitle";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { useTranslation } from "react-i18next";
import { getCustomerCallDetails } from "@/apps/Admin/Pages/Customers/Services";
import { handleSetActiveDetailsMenu } from "@/apps/Admin/Pages/Customers/ClientSideStates";
import { useDispatch, useSelector } from "react-redux";
import UnKnownCustomerOptions from "@/apps/Admin/Pages/AutoDialer/Components/Call/Components/UnKnownCustomerOptions";
import { RootState } from "@/store/Reducers";
import { hanldleSetAutoDialerSocketList } from "@/apps/Admin/Pages/AutoDialer/ClientSideStates";
import TabPane from "antd/es/tabs/TabPane";
import callEndPoints from "@/apps/Admin/Pages/Customers/EndPoints";
import { useQueryClient } from "react-query";

type TargetKey = React.MouseEvent | React.KeyboardEvent | string;

function CallNotification() {
  const dispatch = useDispatch();
  const [connection, setConnection] = useState(null);
  const [isShowCall, setIsShowCall] = useState(false);
  const { activeSocketList } = useSelector(
    (state: RootState) => state.autoDialer
  );
  const queryClient = useQueryClient();
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const [isShowCallDetailsDrawer, setIsShowCallDetailsDrawer] = useState(false);

  const { Text } = Typography;
  const [searchParams, setSearchParams] = useSearchParams();
  const { t } = useTranslation();
  const handleConfirmCancel = (
    key: string,
    type: "all" | "single" = "single"
  ) => {
    Modal.confirm({
      title: t("callNotification.list.warning"),
      icon: null,
      content:
        type === "all"
          ? t("callNotification.list.screenWillBeTrunOff")
          : "Bu Çağrı Kapanacaktır. Onaylıyor musunuz?",
      okText: t("callNotification.list.ok"),
      cancelText: t("callNotification.list.cancel"),
      onOk: async () => {
        if (type === "all") {
          searchParams.delete("callId");
          searchParams.delete("customerId");
          setSearchParams(searchParams);
          setIsShowCallDetailsDrawer(false);
          dispatch(hanldleSetAutoDialerSocketList({ socketList: [] }));
        } else {
          const target = activeSocketList.find(
            (item) => item.customerId === key || item.unSavedNumber === key
          );

          const filterSocketList = activeSocketList.filter(
            (item) => item.customerId !== key && item.unSavedNumber !== key
          );

          if (target) {
            const callIdFromParams = searchParams.get("callId");

            if (target.id && target.id.toString() === callIdFromParams) {
              const targetIndex = activeSocketList.findIndex(
                (item) => item.customerId === key || item.unSavedNumber === key
              );

              const nextItem = activeSocketList[targetIndex + 1];
              const prevItem = activeSocketList[targetIndex - 1];

              // Yeni aktif item olarak bir sonrakini ya da bir öncekini setle
              const newActiveItem = nextItem || prevItem;

              if (newActiveItem) {
                let newParams: any = {};
                if (newActiveItem.id) {
                  newParams["callId"] = newActiveItem.id;
                }
                if (newActiveItem.customerId) {
                  newParams["customerId"] = newActiveItem.customerId;
                }
                if (!newActiveItem.customerId && newActiveItem?.externalParty) {
                  newParams["unSavedNumber"] = newActiveItem?.externalParty;
                }
                console.log("new params", newParams);

                setSearchParams(newParams);
              } else {
                // Eğer başka item yoksa, tüm parametreleri temizle
                searchParams.delete("callId");
                searchParams.delete("customerId");
                setSearchParams(searchParams);
                setIsShowCallDetailsDrawer(false);
              }
            }

            dispatch(
              hanldleSetAutoDialerSocketList({ socketList: filterSocketList })
            );
          }
        }
      },
    });
  };

  const handleGetCallDetails = async (data: any) => {
    try {
      const response = await getCustomerCallDetails(data.id);
      let params: any = {};
      if (response?.Value?.CustomerId) {
        params["customerId"] = response?.Value?.CustomerId;
      }
      if (!data?.customer) {
        params["unSavedNumber"] = data?.externalParty;
      }
      params["callId"] = data.id;
      const sockets = [...activeSocketListRef.current];
      const isAlreadyExists = response?.Value?.CustomerId
      ? sockets.some((s) => s.customerId === response?.Value?.CustomerId)
      : sockets.some((s) => s.unSavedNumber === data?.externalParty);

    if (!isAlreadyExists) {
      sockets.push({ ...params, ...data });
      dispatch(hanldleSetAutoDialerSocketList({ socketList: sockets }));
    }
      
      

      dispatch(handleSetActiveDetailsMenu({ key: "1" }));
      await setSearchParams(params);
      await setSelectedRecord(data);
      setIsShowCallDetailsDrawer(true);
    } catch (error) {
      showErrorCatching(error, null, false, t);
    }
  };

  useEffect(() => {
    // SignalR bağlantısını kuruyoruz
    const newConnection = new HubConnectionBuilder()
      .withUrl(`${setBackEndUrl()}/hubs/callhub`, {
        accessTokenFactory: () => {
          return localStorage.getItem("access_token") || "";
        },
        skipNegotiation: true,
        transport: HttpTransportType.WebSockets,
        withCredentials: true, // CORS için önemli
      })
      .configureLogging(LogLevel.Information)
      .withAutomaticReconnect()
      .build();

    // Bağlantıyı başlatıyoruz
    newConnection
      .start()
      .then(() => {
        console.log("SignalR bağlantısı kuruldu");
        setConnection(newConnection);
      })
      .catch((err) => console.error("SignalR bağlantı hatası:", err));

    // Temizleme fonksiyonu
    return () => {
      if (connection) {
        connection.stop();
      }
    };
  }, []);

  const activeSocketListRef = useRef(activeSocketList);

  useEffect(() => {
    activeSocketListRef.current = activeSocketList;
  }, [activeSocketList]);
  useEffect(() => {
    if (connection) {
      connection.on("CallEvent", async (data: any) => {
        console.log("data---", data);
        if(data?.state===1||data?.State===1)
        {

          handleGetCallDetails(data);
        }
        queryClient.resetQueries({
          queryKey: callEndPoints.getCustomerCallListFilter,
          exact: false,
        });
      });
    }

    return () => {
      if (connection) {
        connection.off("CallEvent");
      }
    };
  }, [connection]);

  return connection ? (
    <>
      {isShowCall && (
        <div className="call-container !w-[250px] !h-[284px] !bg-[#363535] !absolute top-[53px] !right-1 !z-50 !flex !flex-col gap-1 justify-center items-center !rounded-md">
          <div>
            <Text className="!text-white !text-lg">
              {selectedRecord?.callee}
            </Text>
          </div>
          <div>
            <Text className="!text-white !text-lg">
              {selectedRecord?.caller}
            </Text>
          </div>

          <div
            className="!relative !rounded-full !bg-[#4fd964] !p-2 !mt-2 overflow-hidden cursor-pointer"
            onClick={() => {
              setIsShowCallDetailsDrawer(true);
              setIsShowCall(false);
            }}
          >
            <div className="wave absolute inset-0 bg-[#4fd964] opacity-50 rounded-full animate-wave"></div>
            <PhoneFilled className="!text-lg !text-white" />
          </div>
        </div>
      )}

      <Drawer
        width={"100%"}
        bodyStyle={{ padding: "0px" }}
        open={isShowCallDetailsDrawer}
        title={
          <div className="">
            <div className="!flex gap-2 items-center">
              <PageTitle
                title={
                  selectedRecord?.direction?.toLowerCase() === "inbound"
                    ? t("autoDialer.list.incomingCall")
                    : "Giden Arama"
                }
                isSubTitle
              />
              {selectedRecord?.direction?.toLowerCase() === "inbound" ? (
                <ArrowDownOutlined className="!text-xs !text-green-400 !rotate-[45deg]" />
              ) : (
                <ArrowUpOutlined className="!text-xs !text-[#0096d1] !rotate-[45deg]" />
              )}

              <span className="!text-gray-500 !text-sm">
                {selectedRecord?.externalParty}
              </span>
              <MazakaButton
                onClick={() => {
                  handleConfirmCancel("", "all");
                }}
                status="error"
              >
                {t("callNotification.list.cancel")}
              </MazakaButton>
              {!selectedRecord?.customer && <UnKnownCustomerOptions />}
            </div>
          </div>
        }
        // open={true}
        closeIcon={false}
        onClose={() => {
          setIsShowCallDetailsDrawer(false);
        }}
      >
        <Tabs
          hideAdd
          type="editable-card"
          onEdit={(targetKey: any, action: "add" | "remove") => {
            if (action === "remove") {
              handleConfirmCancel(targetKey);
            }
          }}
          onChange={(key) => {
            const target = activeSocketList.find(
              (item) => item.customerId === key || item.unSavedNumber === key
            );

            if (target) {
              const params: any = {};
              if (target.customerId) params["customerId"] = target.customerId;
              else if (target.unSavedNumber)
                params["unSavedNumber"] = target.unSavedNumber;
              params["callId"] = target.id;

              console.log(params);
              setSelectedRecord(target);
              setSearchParams(params);
            }
          }}
        >
          {activeSocketList.map((item) => {
            const key = item.customerId || item.unSavedNumber;
            const label = item.customer || item.unSavedNumber;

            return (
              <TabPane tab={label} key={key} closable={true}>
                <AutoDailerCallIndex />
              </TabPane>
            );
          })}
        </Tabs>
      </Drawer>
    </>
  ) : (
    <PhoneTwoTone twoToneColor="#FF0000" />
  );
  return <></>;
}

export default CallNotification;
