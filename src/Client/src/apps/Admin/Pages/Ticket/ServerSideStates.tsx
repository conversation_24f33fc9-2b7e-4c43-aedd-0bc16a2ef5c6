import { useQuery } from "react-query";
import endpoints from "./EndPoints";
import { getTicketListFilter } from "./Services";

export const useGetTickets = (filter?: any) => {
  const query = useQuery(
    [endpoints.getTicketListFilter, filter],
    () => {
      return getTicketListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};
