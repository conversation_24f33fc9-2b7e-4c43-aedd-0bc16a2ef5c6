import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Form, Row } from "antd";
import { FC, useEffect } from "react";
import { useQueryClient } from "react-query";

import endPoints from "../EndPoints";
import { createSubjectTicket, updateSubjectTicketWithPut } from "../Services";
import { useTranslation } from "react-i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";

const AddOrUpdateSubjectTicket: FC<{
  onFinish: () => void;
  selectedRecord?: any;
}> = ({ onFinish, selectedRecord }) => {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const { t } = useTranslation();
  useEffect(() => {
    if (selectedRecord) {
      form.setFieldsValue({ ...selectedRecord });
    }
  }, [selectedRecord]);

  const hangleOnFinish = async () => {
    const formValues = form.getFieldsValue();

    try {
      if (selectedRecord) {
        await updateSubjectTicketWithPut({ ...selectedRecord, ...formValues });
      } else {
        await createSubjectTicket(formValues);
      }
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
      form.resetFields();
      onFinish();
      queryClient.resetQueries({
        queryKey: endPoints.getSubjectTicketListFilter,
        exact: false,
      });
    } catch (error) {
      showErrorCatching(error,null,false,t)
    }
  };
  return (
    <>
      <MazakaForm
        form={form}
        onFinish={hangleOnFinish}
        submitButtonVisible={false}
      >
        <Row gutter={[20, 20]}>
          <MazakaInput
            label={t("subjectTicket.name")}
            placeholder={t("subjectTicket.name")}
            xs={24}
            name="Name"
            rules={[{ required: true, message: "" }]}
          />

          <Col xs={24}>
            <MazakaButton
              processType={formActions.submitProcessType}
              htmlType="submit"
              status="save"
            >
              {selectedRecord
                ? t("subjectTicket.edit")
                : t("subjectTicket.add")}
            </MazakaButton>
          </Col>
        </Row>
      </MazakaForm>
    </>
  );
};

export default AddOrUpdateSubjectTicket;
