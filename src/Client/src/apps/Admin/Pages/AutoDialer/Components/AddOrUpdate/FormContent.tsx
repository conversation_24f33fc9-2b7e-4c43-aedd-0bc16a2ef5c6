import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { <PERSON><PERSON>, Col, Drawer, Form, Row, Tag } from "antd";
import { useEffect, useState } from "react";
import { <PERSON>zak<PERSON>Button } from "@/apps/Common/MazakaButton";
import SelectDataIndex from "./Customer/SelectDataIndex";
import { MazakaDatePicker } from "@/apps/Common/MazakaDatePicker";
import { BranchesOutlined, SaveOutlined } from "@ant-design/icons";
import GeneralUserQueue from "@/apps/Common/GeneralUserQueue";
import UserQueueList from "./QueueUsers";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { useQueryClient } from "react-query";
import useMazakaForm from "@/hooks/useMazakaForm";
import { createAutoDialer, updateAutoDialerWithPut } from "../../Services";
import endPoints from "../../EndPoints";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { useTranslation } from "react-i18next";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { useNavigate, useParams } from "react-router-dom";
import {
  useGetAutoDialerDetails,
  useGetUserQueues,
} from "../../ServerSideStates";
import dayjs from "dayjs";
import {
  handleResetAllFieldsCustomer,
  hanldleSetCustomerFilter,
} from "../../../Customers/ClientSideStates";
import {
  hanldleSetAutoDialerIshowSelectDataDrawer,
  hanldleSetAutoDialerSelectDataTab,
} from "../../ClientSideStates";
import { FormInstance } from "antd/es/form";
import { FormInstance as FormInstanceType } from "antd";
import { MazakaSwitch } from "@/apps/Common/MazakaSwitch";

const FormContent = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { filter, customersListSelectedItems } = useSelector(
    (state: RootState) => state.customer
  );
  const { isShowSelectDataDrawer } = useSelector(
    (state: RootState) => state.autoDialer
  );
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const allQueueNumbers = useGetUserQueues();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const [selectedQueue, setSelectedQueue] = useState<null | any>(null);
  const { autoDialerId } = useParams();
  const autoDialerDetails = useGetAutoDialerDetails(autoDialerId || "");

  useEffect(() => {
    if (autoDialerDetails?.data?.Value) {
      const data = { ...autoDialerDetails?.data?.Value };
      data["StartDate"] = dayjs(data["StartDate"]);
      const queueNumber = data["QueueNumber"];
      const findItem = allQueueNumbers?.data?.Value?.find((item: any) => {
        return item.QueueNumber === queueNumber;
      });

      if (findItem) {
        setSelectedQueue({
          label: findItem?.QueueName,
          value: findItem.QueueNumber,
          agents: findItem.Agents,
        });
      }

      form.setFieldsValue({
        ...data,
      });
    }
  }, [autoDialerDetails?.data?.Value, allQueueNumbers?.data?.Value]);

  useEffect(() => {
    if (!autoDialerId) {
      form.resetFields();
    }
  }, [autoDialerId]);

  const queueNumber = Form.useWatch("QueueNumber", form);

  useEffect(() => {
    if (queueNumber) {
      const findItem = allQueueNumbers?.data?.Value?.find(
        (item: any) => item.QueueNumber === queueNumber
      );
      if (findItem) {
        setSelectedQueue({
          label: findItem?.QueueName,
          value: findItem.QueueNumber,
          agents: findItem.Agents,
        });
      } else {
        setSelectedQueue(null);
      }
    } else {
      setSelectedQueue(null);
    }
  }, [queueNumber, allQueueNumbers?.data?.Value]);

  const handleOnFinish = async () => {
    const formValues = form.getFieldsValue();
    formValues["StartDate"] = dayjs(formValues["StartDate"]).format(
      "YYYY-MM-DDTHH:mm"
    );

    if (
      !autoDialerDetails?.data?.Value?.Id &&
      customersListSelectedItems?.length <= 0
    ) {
      openNotificationWithIcon("error", "Müşteri Seçimi zorunlu");
      return false;
    }

    const targetNumbers =
      autoDialerDetails?.data?.Value?.Id &&
      customersListSelectedItems?.length <= 0
        ? autoDialerDetails?.data?.Value?.TargetNumbers
        : customersListSelectedItems.map((item: any) => `+${item?.PhonePrefix}${item?.Phone}`) || [];

    formValues["TargetNumbers"] = targetNumbers;

    try {
      if (autoDialerDetails?.data?.Value?.Id) {
        await updateAutoDialerWithPut({
          ...autoDialerDetails?.data?.Value,
          ...formValues,
        });
      } else {
        const response = await createAutoDialer(formValues);
       
      }

      openNotificationWithIcon("success", t("form.transactionSuccessful"));
      dispatch(handleResetAllFieldsCustomer());

      queryClient.resetQueries({
        queryKey: endPoints.getAutoDialerListFilter,
        exact: false,
      });
      navigate(`/admin/auto-dailer`);
    } catch (error) {
      showErrorCatching(error, mazakaForm, false, t);
    }
  };

  return (
    <Row>
      <Col xs={24}>
        <MazakaForm
          form={form}
          onFinish={handleOnFinish}
          submitButtonVisible={false}
          initialValues={{ Active: true }}
        >
          <Row gutter={[20, 20]}>
            <MazakaInput
              rules={[{ required: true, message: "" }]}
              label={t("autoDialer.list.description")}
              placeholder={t("autoDialer.list.description")}
              name="Name"
              xs={24}
              md={12}
              xl={6}
            />
            <GeneralUserQueue
              rules={[{ required: true, message: "" }]}
              label={t("autoDialer.list.queue")}
              placeholder={t("autoDialer.list.queue")}
              name="QueueNumber"
              xs={24}
              md={12}
              xl={6}
              size="middle"
              onChange={async (value: string, obj: any) => {
                if (value && obj) {
                  setSelectedQueue({
                    label: obj?.label,
                    value: obj?.value,
                    agents: obj?.agents,
                  });
                } else {
                  setSelectedQueue(null);
                }
                form.setFieldValue("QueueNumber", value);
              }}
            />
            <MazakaDatePicker
              disablePastDates={true}
              disablePastTimes={true}
              rules={[{ required: true, message: "" }]}
              label={t("autoDialer.list.startDate")}
              name={"StartDate"}
              showTime={true}
              xs={24}
              md={12}
              xl={6}
            />
            <MazakaSwitch
              label={t("users.add.status")}
              xs={24}
              md={12}
              xl={6}
              name={"Active"}
              checkedChildren={t("users.add.active")}
              unCheckedChildren={t("users.add.pasive")}
              onChange={(status: boolean) => {
                form.setFieldValue("Active", status);
              }}
            />
            <Col xs={24} md={12} lg={6} className="!flex items-end">
              <MazakaButton
                className="!mt-3"
                onClick={async () => {
                  const currentFilter = { ...filter };
                  currentFilter["Status"] = 1;
                  dispatch(hanldleSetCustomerFilter({ filter: currentFilter }));
                  dispatch(
                    hanldleSetAutoDialerIshowSelectDataDrawer({ status: true })
                  );
                }}
              >
                {t("autoDialer.list.addCallingData")}
              </MazakaButton>
            </Col>
            {selectedQueue && (
              <Col xs={24}>
                <UserQueueList data={selectedQueue?.agents} />
              </Col>
            )}
            {(() => {
              const selectedItems =
                customersListSelectedItems?.length > 0
                  ? customersListSelectedItems
                  : autoDialerDetails?.data?.Value?.TargetNumbers;

              if (!selectedItems?.length) return null;

              return (
                <Col xs={24}>
                  <Row gutter={[0, 10]}>
                    <Col xs={24} className="!flex items-center gap-1">
                      <span className="!font-bold">
                        {t("autoDialer.list.numberSelectedCustomers")}
                      </span>
                    </Col>

                    <Col xs={24} className="!flex gap-1">
                      <Tag color="#35b214">{selectedItems.length}</Tag>

                      {selectedItems.map((item: any, index: number) => (
                        <span key={index}>
                          <Tag>
                            {typeof item === "string"
                              ? item
                              :  `${item?.PhonePrefix}${item?.Phone}`}
                          </Tag>
                        </span>
                      ))}
                    </Col>

                    <Col xs={24}>
                      <div className="!flex gap-1">
                        <MazakaButton
                          status="save"
                          icon={<SaveOutlined />}
                          type="link"
                          className="!text-blue-500"
                          htmlType="submit"
                        >
                          {t("autoDialer.list.save")}
                        </MazakaButton>

                        <MazakaButton
                          status="error"
                          icon={<BranchesOutlined />}
                          type="link"
                          className="!text-blue-500"
                          htmlType="button"
                          onClick={async () => {
                            const currentFilter = { ...filter, Status: 1 };
                            dispatch(
                              hanldleSetCustomerFilter({
                                filter: currentFilter,
                              })
                            );
                            dispatch(
                              hanldleSetAutoDialerIshowSelectDataDrawer({
                                status: true,
                              })
                            );
                          }}
                        >
                          {t("Yeniden Seç")}
                        </MazakaButton>
                      </div>
                    </Col>
                  </Row>
                </Col>
              );
            })()}
          </Row>
        </MazakaForm>
      </Col>
      <Drawer
        width={"100%"}
        bodyStyle={{ padding: 14 }}
        destroyOnClose
        title={
          <>
            <MazakaButton
              icon={<SaveOutlined />}
              onClick={() => {
                dispatch(
                  hanldleSetAutoDialerIshowSelectDataDrawer({ status: false })
                );
                dispatch(hanldleSetAutoDialerSelectDataTab({ key: "1" }));
                const currentFilter = { ...filter };
                delete currentFilter["Status"];
                dispatch(hanldleSetCustomerFilter({ filter: currentFilter }));
              }}
              status={`${
                customersListSelectedItems?.length <= 0 ? "error" : "save"
              }`}
              disabled={customersListSelectedItems?.length <= 0}
            >
              {`${t("autoDialer.list.selectAndCountinue")} ${
                customersListSelectedItems?.length > 0
                  ? `(${customersListSelectedItems?.length} ${t(
                      "autoDialer.list.itemSelected"
                    )})`
                  : ""
              }`}
            </MazakaButton>
          </>
        }
        open={isShowSelectDataDrawer}
        onClose={() => {
          dispatch(hanldleSetAutoDialerSelectDataTab({ key: "1" }));
          dispatch(
            hanldleSetAutoDialerIshowSelectDataDrawer({ status: false })
          );
          const currentFilter = { ...filter };
          delete currentFilter["Status"];
          dispatch(hanldleSetCustomerFilter({ filter: currentFilter }));
        }}
      >
        <SelectDataIndex />
      </Drawer>
    </Row>
  );
};

export default FormContent;
