import { MazakaButton } from "@/apps/Common/MazakaButton";
import { PlusOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { handleSetCustomersListSelectedItems } from "../../Customers/ClientSideStates";
import { useQueryClient } from "react-query";
import endPoints from "@/apps/Admin/Pages/AutoDialer/EndPoints";

const AddOtoDailerButton = () => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  return (
    <>
      <MazakaButton
        icon={<PlusOutlined />}
        status="save"
        onClick={() => {
          navigate("/admin/add-auto-dialer");
          dispatch(
            handleSetCustomersListSelectedItems({
              selectedIds: [],
              selectedItems: [],
            })
          );
          queryClient.resetQueries({
            queryKey: endPoints.getAutoDialerDetails,
            exact: false,
          });
        }}
      >
        {t("autoDialer.list.add")}
      </MazakaButton>
    </>
  );
};

export default AddOtoDailerButton;
