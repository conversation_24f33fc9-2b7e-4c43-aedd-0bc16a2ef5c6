import { DeleteOutlined, FormOutlined } from "@ant-design/icons";
import { Col, Drawer, Modal, Table, Tag, Tooltip, Typography } from "antd";
import endPoints from "../EndPoints";
import { useQueryClient } from "react-query";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import {
  determineTicketNotificationWay,
  determineTicketPriority,
} from "@/helpers/Ticket";
import { useGetTasks } from "../ServerSideStates";
import { deleteTask } from "../Services";
import { hanldleSetTaskFilter } from "../ClientSideStates";
import AddOrUpdateTask from "./AddOrUpdate/AddOrUpdateTaskIndex";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { excludeUnnesessaryKey } from "@/helpers/ExcludeUnNesessaryKey";

const ListItems = () => {
  const { Text } = Typography;
  const [isShowEditDrawer, setIsShowEditDrawer] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const { filter } = useSelector((state: RootState) => state.task);
  const task = useGetTasks(excludeUnnesessaryKey(filter));
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const columns = [
    {
      title: t("task.list.title"),
      dataIndex: "Title",
      key: "Title",
      sorter: (a: any, b: any) => a.Title.localeCompare(b.Title),
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },
    {
      title: t("task.list.assignedUser"),
      dataIndex: "UserFullName",
      key: "UserFullName",
      sorter: (a: any, b: any) =>
        a?.UserFullName.localeCompare(b?.UserFullName),
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },
    {
      title: t("task.list.reporterUser"),
      dataIndex: "ReporterUserFullName",
      key: "Subject",
      sorter: (a: any, b: any) =>
        a?.ReporterUserFullName.localeCompare(b?.ReporterUserFullName),
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },
    {
      title: t("task.list.status"),
      dataIndex: ["Status", "Name"],
      key: "Status",
      sorter: (a: any, b: any) =>
        a?.Status?.Name?.localeCompare(b?.Status?.Name),
      render: (value: string) => {
        return (
          <>
            <Tag color="#0096d1" className="!text-xs">
              {value}
            </Tag>
          </>
        );
      },
    },
    {
      title: t("task.list.departments"),
      dataIndex: "Departments",
      key: "Departments",

      render: (value: any[]) => {
        return (
          <>
            {value?.length > 0 && (
              <>
                {value?.map((item: any) => {
                  return (
                    <>
                      <span>
                        <Tag color="#0096d1">{item?.DepartmentName || ""}</Tag>
                      </span>
                    </>
                  );
                })}
              </>
            )}
          </>
        );
      },
    },
    {
      title: t("task.list.notificationWay"),
      dataIndex: "NotificationWay",
      key: "Notification Way",
      sorter: (a: any, b: any) =>a?.NotificationWay-b?.NotificationWay      ,
  
      render: (value: number) => {
        return (
          <>
            <Text className="!text-xs">
             {value||""}
            </Text>
          </>
        );
      },
    },
    {
      title: t("task.list.priority"),
      dataIndex: "Priority",
      key: "Priority",
      sorter: (a: any, b: any) =>
        determineTicketPriority(a?.Priority, t)?.localeCompare(
          determineTicketPriority(b?.Priority, t)
        ),
      render: (value: number) => {
        return (
          <>
            <Text className="!text-xs">
              {determineTicketPriority(value, t)}
            </Text>
          </>
        );
      },
    },
    {
      title: t("task.list.endDate"),
      dataIndex: "EndDate",
      key: "EndDate",

      render: (value: number) => {
        return (
          <>
            {value && (
              <Text className="!text-xs">
                {dayjs(value).format("YYYY-MM.DD HH:mm")}
              </Text>
            )}
          </>
        );
      },
    },

    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <Col className="!flex gap-2 justify-end !px-2">
          <Tooltip title={t("ticket.list.edit")}>
            <FormOutlined
              className=" !text-[#0096d1] !text-sm"
              onClick={async (e) => {
                e.preventDefault();
                e.stopPropagation();
                await setSelectedRecord(record);
                setIsShowEditDrawer(true);
              }}
            />
          </Tooltip>

          <Tooltip title={t("ticket.list.delete")}>
            <DeleteOutlined
              className=" !text-[#9da3af] !text-sm"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                confirm(record);
              }}
            />
          </Tooltip>
        </Col>
      ),
    },
  ];

  const confirm = (record: any) => {
    Modal.confirm({
      title: t("task.list.warning"),
      icon: null,
      content: t("task.list.deleteModalDesc"),
      okText: t("task.list.delete"),
      cancelText: t("task.list.cancel"),
      onOk: async () => {
        try {
          await deleteTask(record);
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          queryClient.resetQueries({
            queryKey: endPoints.getTaksListFilter,
            exact: false,
          });
        } catch (error: any) {
          showErrorCatching(error, null, false, t);
        }
      },
    });
  };
  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
    dispatch(hanldleSetTaskFilter({ filter: newFilter }));
  };
  return (
    <>
      <Table
        columns={columns}
        dataSource={task?.data?.Value}
        loading={task.isLoading || task.isFetching}
        scroll={{x:700}}
        onRow={(record) => {
          return {
            onClick: async (event) => {
              await setSelectedRecord(record);
              setIsShowEditDrawer(true);
            },
          };
        }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: task.data?.FilteredCount || 0,
          current: task.data?.PageNumber,
          pageSize: task.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
        rowKey={"Id"}
      />
      <Drawer
        width={"80%"}
        title={t("task.list.editTask")}
        open={isShowEditDrawer}
        onClose={() => setIsShowEditDrawer(false)}
      >
        <AddOrUpdateTask
          selectedRecord={selectedRecord}
          onFinish={() => setIsShowEditDrawer(false)}
        />
      </Drawer>
    </>
  );
};

export default ListItems;
