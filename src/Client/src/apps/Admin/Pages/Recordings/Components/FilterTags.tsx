import { useSelector } from "react-redux";

import { RootState } from "@/store/Reducers";
import GeneralFilterTags from "../../Users/<USER>/GeneralFilterTags";
import { hanldleSetRecordingsFilter } from "../ClientSideStates";



const FilterTags = () => {
    const {filter} = useSelector((state:RootState)=>state.recordings)
    return ( <>
    
    <GeneralFilterTags
    isDontCloseableDate={true}
    showFilterTagLength={2}
    filter={filter}
    actionFunc={hanldleSetRecordingsFilter}
    actionFunkKey="filter"
    excludedKeys={["PageNumber","PageSize","Direction","IsAnswered"]}
    />
    </> );
}
 
export default FilterTags;