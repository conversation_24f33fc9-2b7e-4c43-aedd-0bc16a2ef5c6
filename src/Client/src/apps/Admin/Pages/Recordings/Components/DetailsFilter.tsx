import { MazakaForm } from "@/apps/Common/MazakaForm";
import useMazakaForm from "@/hooks/useMazakaForm";
import { RootState } from "@/store/Reducers";
import { Col, Form, Row } from "antd";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { hanldleSetRecordingsFilter } from "../ClientSideStates";
import dayjs from "dayjs";
import { FC, useEffect, useState } from "react";
import { MazakaRangePicker } from "@/apps/Common/MazakaRangePicker";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import GeneralUsers from "@/apps/Common/GenerralUsers";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import GeneralCustomerWithSearch from "@/apps/Common/GeneralCustomerWIthSearch";
import { MazakaInput } from "@/apps/Common/MazakaInput";

const DetailsFilter: FC<{ onFinish: any }> = ({ onFinish }) => {
  const { filter } = useSelector((state: RootState) => state.recordings);
  const [directionType, setDirectionType] = useState<string | null>(null);
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const directionStatusOptions = [
    { label: t("customers.add.inbound"), value: "INBOUND" },
    { label: t("customers.add.outbound"), value: "OUTBOUND" },
  ];

  const { formActions, mazakaForm } = useMazakaForm(form);
  useEffect(() => {
    const startDate = filter?.StartDate;
    const endDate = filter?.EndDate;
    const dateRange = [];
    if (startDate) {
      dateRange.push(dayjs(startDate));
    }
    if (endDate) {
      dateRange.push(dayjs(endDate));
    }

    form.setFieldsValue({
      ExtensionNumbers: filter?.ExtensionNumbers,
      Date: dateRange,
      Direction: filter?.Direction,
      Caller: filter?.Caller,
      Callee: filter?.Callee,
      IsAnswered:
        typeof filter?.IsAnswered === "boolean"
          ? Number(filter?.IsAnswered)
          : undefined,
    });
    if (!filter?.Direction) {
      setDirectionType(null);
      form.resetFields(["Customer", "Caller", "Calle"]);
    }
  }, [filter]);
  const handleOnFinish = async () => {
    const formValues = form.getFieldsValue();

    let currentFilter = { ...filter };

    for (let key in formValues) {
      const value = formValues[key];

      const isFalsyAndNotZero = value !== 0 && !value;
      const isEmptyArray = Array.isArray(value) && value.length === 0;

      if (isFalsyAndNotZero || isEmptyArray) {
        delete formValues[key];
        delete currentFilter[key];

        if (key === "Date") {
          delete currentFilter["StartDate"];
          delete currentFilter["EndDate"];
        }
      }
    }

    delete formValues["Customer"];
    delete formValues["User"];
    if (formValues?.Date && formValues["Date"][0] && formValues["Date"][1]) {
      formValues["StartDate"] = dayjs(formValues["Date"][0]).format(
        "YYYY-MM-DDTHH:mm"
      );
      formValues["EndDate"] = dayjs(formValues["Date"][1]).format("YYYY-MM-DDTHH:mm");
      delete formValues["Date"];
    }
    // if (formValues["customNameExtensionNumbers"]?.length > 0) {
    //   // formValues["ExtensionNumbers"] = formValues["customNameExtensionNumbers"]
    //   //   ?.filter((item: any) =>
    //   //     formValues["ExtensionNumbers"]?.includes(item?.value)
    //   //   )
    //   //   ?.map((item: any) => item.extension);

    //   formValues["customNameExtensionNumbers"] = JSON.parse(
    //     JSON.stringify(formValues["customNameExtensionNumbers"])
    //   );
    // }
    const newFilter = { ...currentFilter, ...formValues };

    await dispatch(
      hanldleSetRecordingsFilter({
        filter: newFilter,
      })
    );
    mazakaForm.setSuccess(1000, () => {}, t("form.transactionSuccessful"));
    onFinish();
  };
  const callStatusOptions = [
    { label: t("recording.answerd"), value: 1 },
    { label: t("recording.missed"), value: 0 },
  ];

  console.log(filter);

  return (
    <>
      <MazakaForm
        form={form}
        submitButtonVisible={false}
        onFinish={handleOnFinish}
      >
        <Row gutter={[20, 20]}>
          <GeneralUsers
            label={t("users.list.extension")}
            placeholder={t("users.list.extension")}
            xs={24}
            allowClear
            mode="multiple"
            name="ExtensionNumbers"
            extensionStatus="add"
            onChange={(value: number, obj: any) => {
              form.setFieldValue(
                "customNameExtensionNumbers",
                JSON.parse(JSON.stringify(obj))
              );
            }}
          />
          <MazakaSelect
            placeholder={t("customers.add.direction")}
            label={t("customers.add.direction")}
            xs={24}
            allowClear
            name="Direction"
            options={directionStatusOptions}
            onChange={(value: string, obj: any) => {
              form.setFieldValue("customNameDirection", obj.label);
              setDirectionType(value);
            }}
          />

          <>
            <GeneralUsers
              label={t("recording.caller")}
              placeholder={t("recording.caller")}
              xs={24}
              allowClear
              onChange={(value: string, obj: any) => {
                console.log("obj",obj)
                form.setFieldValue("Caller", obj?.PhoneNumber);
                form.resetFields(["User"]);
              }}
              name="User"
              extensionStatus="add"
            />
            <MazakaInput
              label={t("recording.callerPhoneNumber")}
              placeholder={t("recording.callerPhoneNumber")}
              name="Caller"
              xs={24}
              onChange={(e: any) => {
                let formValues = form.getFieldsValue();
                if (formValues["User"]) {
                  form.resetFields(["User"]);
                }
                form.setFieldValue("Caller", e.target.value);
              }}
            />

            <GeneralCustomerWithSearch
              label={t("recording.callee")}
              placeholder={t("recording.callee")}
              xs={24}
              allowClear
              onChange={(value: string, obj: any) => {
                form.setFieldValue("Callee", obj?.Phone || "");
                form.resetFields(["Customer"]);
              }}
              name="Customer"
            />

            <MazakaInput
              label={t("recording.calleePhoneNumber")}
              placeholder={t("recording.calleePhoneNumber")}
              name="Callee"
              xs={24}
              onChange={(e: any) => {
                let formValues = form.getFieldsValue();
                if (formValues["Custoemr"]) {
                  form.resetFields(["Customer"]);
                }
                form.setFieldValue("Callee", e.target.value);
              }}
            />
          </>

          {directionType === "INBOUND" && (
            <>
              <GeneralCustomerWithSearch
                label={t("customers.customers")}
                placeholder={t("customers.customers")}
                xs={24}
                allowClear
                onChange={(value: string, obj: any) => {
                  form.setFieldValue("Customer", value);
                  form.setFieldValue("Callee", obj?.Phone || "");
                }}
                name="Customer"
              />

              <MazakaInput
                type="number"
                label={t("users.add.phone")}
                placeholder={t("users.add.phone")}
                name="Callee"
                xs={24}
                onChange={(e: any) => {
                  form.setFieldValue("Callee", e.target.value);
                }}
              />
            </>
          )}

          <MazakaSelect
            label={t("customers.add.status")}
            placeholder={t("customers.add.status")}
            xs={24}
            allowClear
            name="IsAnswered"
            options={callStatusOptions}
            onChange={(value: number, obj: any) => {
              form.setFieldValue("customNameIsAnswered", obj.label);
            }}
          />
          <MazakaRangePicker
            label={t("task.list.dateRange")}
            name={"Date"}
            xs={24}
            className="!m-0"
            showTime
          />

          <Col xs={24}>
            <MazakaButton
              htmlType="submit"
              processType={formActions.submitProcessType}
              status="save"
            >
              {t("users.filter.filterButton")}
            </MazakaButton>
          </Col>

          <Form.Item
            name={"customNameExtensionNumbers"}
            className="!hidden"
          ></Form.Item>
          <Form.Item
            name={"customNameDirection"}
            className="!hidden"
          ></Form.Item>
          <Form.Item
            name={"customNameIsAnswered"}
            className="!hidden"
          ></Form.Item>
          <Form.Item name={"customNameCaller"} className="!hidden"></Form.Item>
        </Row>
      </MazakaForm>
    </>
  );
};

export default DetailsFilter;
