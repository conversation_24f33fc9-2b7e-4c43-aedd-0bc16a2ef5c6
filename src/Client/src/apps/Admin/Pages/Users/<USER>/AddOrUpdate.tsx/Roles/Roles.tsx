import React, { useEffect, useState } from "react";
import { Checkbox, Col, Form, Row, Skeleton, TreeSelect } from "antd";
import {
  useGetAllPermissions,
  useGetUserOrRolePermissions,
} from "@/apps/Admin/Pages/Authority/ServerSideStates";
import CustomNoData from "@/apps/Common/CustomNoData";
import { useSharedForm } from "@/hooks/useSharedForm";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";

const Roles: React.FC = () => {
  const { t } = useTranslation();
  const form = useSharedForm();
  const allPermissions = useGetAllPermissions();
  const [treeData, setTreeData] = useState<any[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<any[]>([]);
  const [checkAll, setCheckAll] = useState(false);

  const params = useParams();
  const userId = params["userId"];
  const userPermissions = useGetUserOrRolePermissions(
    userId ? { UserId: userId } : null
  );

  interface PermissionItem {
    Id: string;
    Name: string;
    TopPermissionId?: string | null;
  }
  
  function buildPermissionTree(data:any) {
    const map = new Map();
    const roots = [];
  
    // Her item'i node olarak ekle
    for (const item of data) {
      map.set(item.Id, {
        title: item.Name,
        value: item.Id,
        children: [],
        disabled: false, // default
      });
    }
  
    // Parent-child ilişkisi kur
    for (const item of data) {
      const node = map.get(item.Id);
      if (item.TopPermissionId && map.has(item.TopPermissionId)) {
        map.get(item.TopPermissionId).children.push(node);
      } else {
        roots.push(node);
      }
    }
  
    // Ebeveyn node'ları disable et
   
  
    return roots;
  }
  
  
  useEffect(() => {
  
   setTreeData(buildPermissionTree(allPermissions?.data?.Value||[]))
  }, [allPermissions.data]);

  useEffect(() => {
    if (userPermissions.data?.Value && allPermissions.data?.Value) {
      const permissionIds = userPermissions.data?.Value?.map(
        (item: any) => item?.PermissionId
      );
      setCheckedKeys(permissionIds);
      form.setFieldValue("PermissionIds", permissionIds);
    }
  }, [userPermissions.data, allPermissions.data]);

  const handleCheckAllChange = (e: any) => {
    const isChecked = e.target.checked;
    setCheckAll(isChecked);

    const allKeys =
      allPermissions?.data?.Value?.map((item: any) => item.Id) || [];
    setCheckedKeys(isChecked ? allKeys : []);
    form.setFieldsValue({ PermissionIds: isChecked ? allKeys : [] });
  };

 

  return (
    <Row gutter={[16, 16]}>
      <Col xs={24}>
        <Skeleton
          loading={allPermissions.isLoading || allPermissions.isFetching}
        >
          {treeData.length > 0 ? (
            <Row>
              <Col xs={24} md={18}>
               <Form.Item name={"PermissionIds"} >

                  <TreeSelect
                    treeData={treeData}
                    treeCheckable
                    treeCheckStrictly
                    onChange={(value) => {
               
                      setCheckedKeys(value);
                      form.setFieldValue("PermissionIds", value);
                    }}
                    treeExpandedKeys={expandedKeys}
                    onTreeExpand={(expandedKeys) =>
                      setExpandedKeys(expandedKeys)
                    }
                    showCheckedStrategy={TreeSelect.SHOW_ALL}
                    style={{ width: "100%" }}
                  />
               </Form.Item>
               
              </Col>
              <Col xs={24}>
                <Checkbox checked={checkAll} onChange={handleCheckAllChange}>
                  <span className="!text-xs">
                    {checkAll
                      ? t("authority.removeSelectAll")
                      : t("authority.selectAll")}
                  </span>
                </Checkbox>
              </Col>
            </Row>
          ) : (
            <CustomNoData description="Aktif yetki bulunamadı" />
          )}
        </Skeleton>
      </Col>
    </Row>
  );
};

export default Roles;
