import { Checkbox, Col, Form, Row, Skeleton, TreeSelect } from "antd";
import {
  useGetAllPermissions,
  useGetUserOrRolePermissions,
} from "../../ServerSideStates";
import GeneralRoles from "@/apps/Common/GeneralRoles";
import { FC, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSharedForm } from "@/hooks/useSharedForm";

const FormContent: FC<{
  selectedRoleId: null | string;
  setSelectedRoleId: any;
}> = ({ selectedRoleId, setSelectedRoleId }) => {
  const { t } = useTranslation();
  const form = useSharedForm();
  const permissions = useGetAllPermissions();
  const rolePermissions = useGetUserOrRolePermissions(
    selectedRoleId ? { RoleId: selectedRoleId } : null
  );
  const [treeData, setTreeData] = useState<any[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<any[]>([]);
  const [checkAll, setCheckAll] = useState(false);

  const convertTreeData = () => {
    const data = permissions.data?.Value || [];

    const grouped: Record<string, any[]> = {};
    data.forEach((item: any) => {
      const parentId = item.TopPermissionId ?? "root";
      if (!grouped[parentId]) grouped[parentId] = [];
      grouped[parentId].push(item);
    });

    const buildTree = (parentId: string | null): any[] => {
      const key = parentId ?? "root";
      return (grouped[key] || []).map((item: any) => ({
        title: item.Name,
        key: item.Id,
        value: item.Id,
        children: buildTree(item.Id),
      }));
    };

    const tree = buildTree(null);
    setTreeData(tree);
    setExpandedKeys(data.map((item: any) => item.Id));
  };

  useEffect(() => {
    convertTreeData();
  }, [permissions.data]);

  useEffect(() => {
    if (rolePermissions.data?.Value && permissions.data?.Value) {
      const permissionIds = rolePermissions.data.Value.map(
        (item: any) => item.PermissionId
      );
      setCheckedKeys(permissionIds);
      form.setFieldValue("PermissionIds", permissionIds);
      setCheckAll(
        permissions.data.Value.every((perm: any) =>
          permissionIds.includes(perm.Id)
        )
      );
    }
  }, [rolePermissions.data, selectedRoleId, permissions.data]);

  const handleCheckAllChange = (e: any) => {
    const isChecked = e.target.checked;
    setCheckAll(isChecked);

    const allKeys = permissions?.data?.Value?.map((item: any) => item.Id) || [];
    setCheckedKeys(isChecked ? allKeys : []);
    form.setFieldsValue({ PermissionIds: isChecked ? allKeys : [] });
  };

  

  return (
    <Row gutter={[0, 10]}>
      <GeneralRoles
        xs={24}
        label={t("authority.role")}
        placeholder={t("authority.role")}
        name={"RoleId"}
        onChange={(value: string) => {
          setSelectedRoleId(value);
        }}
      />
      <Col xs={24}>
        <Row gutter={[0, 0]}>
          <Col xs={24}>
            <Skeleton loading={permissions.isLoading || permissions.isFetching}>
              {treeData.length > 0 ? (
                <Col xs={24}>
                  <Form.Item name={"PermissionIds"}>
                    <TreeSelect
                      treeData={treeData}
                      treeCheckable
                      treeCheckStrictly
                      value={checkedKeys}
                      onChange={(value) => {
                        setCheckedKeys(value);
                        form.setFieldValue("PermissionIds", value);
                      }}
                      treeExpandedKeys={expandedKeys}
                      onTreeExpand={(keys) => setExpandedKeys(keys)}
                      showCheckedStrategy={TreeSelect.SHOW_ALL}
                      style={{ width: "100%" }}
                    />
                  </Form.Item>
                </Col>
              ) : null}
            </Skeleton>
          </Col>
          <Col xs={24}>
            <Form.Item>
              <Checkbox checked={checkAll} onChange={handleCheckAllChange}>
                <span className="!text-xs">
                  {checkAll
                    ? t("authority.removeSelectAll")
                    : t("authority.selectAll")}
                </span>
              </Checkbox>
            </Form.Item>
          </Col>
        </Row>
      </Col>
    </Row>
  );
};

export default FormContent;
