import { Modal } from "antd";
import { useEffect, useState } from "react";
import { setBackEndUrl } from "@/helpers/SetBackEndUrl";
import {
  HubConnectionBuilder,
  LogLevel,
  HttpTransportType,
} from "@microsoft/signalr";
import CountdownTimer from "./CounterTime";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import dayjs from "dayjs";

const PauseHub = () => {
  const [connection, setConnection] = useState(null);
  const [isShowPauseCounter, setIsShowPauseCounter] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const { userInfoes } = useSelector((state: RootState) => state.profile);
  useEffect(() => {
    // SignalR bağlantısını kuruyoruz
    const newConnection = new HubConnectionBuilder()
      .withUrl(`${setBackEndUrl()}/hubs/pausehub`, {
        accessTokenFactory: () => {
          return localStorage.getItem("access_token") || "";
        },
        skipNegotiation: true,
        transport: HttpTransportType.WebSockets,
        withCredentials: true, // CORS için önemli
      })
      .configureLogging(LogLevel.Information)
      .withAutomaticReconnect()
      .build();

    // Bağlantıyı başlatıyoruz
    newConnection
      .start()
      .then(() => {
        console.log("SignalR bağlantısı kuruldu");
        setConnection(newConnection);
      })
      .catch((err) => console.error("SignalR bağlantı hatası:", err));

    // Temizleme fonksiyonu
    return () => {
      if (connection) {
        connection.stop();
      }
    };
  }, []);

  useEffect(() => {
    if (connection) {
      connection.on("PauseStartEvent", async (data) => {
        console.log("PauseEvent", data);
        setSelectedRecord(data);
        setIsShowPauseCounter(true);
      });
    }

    return () => {
      if (connection) {
        connection.off("PauseStartEvent");
      }
    };
  }, [connection]);

  useEffect(() => {
    if (userInfoes?.ActivePause) {
      const startDateTimeStr = userInfoes.ActivePause.StartDateTime;
      const durationMinutes = userInfoes.ActivePause.Duration;

      const start = dayjs(startDateTimeStr);
      const now = dayjs();

      const diffMinutes = now.diff(start, "minute");
      const remainingMinutes = durationMinutes - diffMinutes;

      setSelectedRecord({
        pauseId: userInfoes.ActivePause.Id,
        duration: remainingMinutes,
      });

      setIsShowPauseCounter(true);
    }
  }, [userInfoes]);

  return (
    <>
      <Modal
        title={false}
        open={isShowPauseCounter}
        closeIcon={false}
        footer={false}
      >
        {selectedRecord && (
          <CountdownTimer
            selectedRecord={selectedRecord}
            onFinish={() => {
              setIsShowPauseCounter(false);
            }}
          />
        )}
      </Modal>
    </>
  );
};

export default PauseHub;
