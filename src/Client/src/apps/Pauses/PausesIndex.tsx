import { Col, Row } from "antd";
import Title from "./Components/Title";
import TopOptions from "./Components/TopOptions";
import ListItems from "./Components/ListItems";
import { FC, useEffect } from "react";
import { useQueryClient } from "react-query";
import endPoints from "./EndPoints"

interface PausesProps{
    role:"admin" |"simpleUser"
}


const PausesIndex:FC<PausesProps> = ({role}) => {
    const queryClient = useQueryClient()
    useEffect(()=>{
        queryClient.resetQueries({
          queryKey: endPoints.getPausesListFilterByOdata,
          exact: false,
        });
      },[])

    return ( <>
     <Col xs={24}  >
        <Row gutter={[0,0]} >
            <Col xs={24}  >
              <Title/>
            </Col>
            <Col xs={24}  >
               <TopOptions role={role} />
            </Col>
            <Col xs={24} className="" >
                <ListItems role={role} />
            </Col>
            
        </Row>
    </Col>
    </> );
}
 
export default PausesIndex;