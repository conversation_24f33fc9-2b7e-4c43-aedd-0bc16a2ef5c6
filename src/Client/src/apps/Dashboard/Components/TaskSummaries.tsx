
import { Col, Row, Table, Tag, Typography } from "antd";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { determineTicketPriority } from "@/helpers/Ticket";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";
import { useGetTasks } from "@/apps/Admin/Pages/Task/ServerSideStates";
import { useNavigate } from "react-router-dom";

const TaskSummaries = () => {
  const { Text } = Typography;
  const { filter } = useSelector((state: RootState) => state.dashboard);
  const task = useGetTasks({ ...filter, PageSize: 100, PageNumber: 1 });
  const { t } = useTranslation();
  const navigate = useNavigate()
  

  const columns = [
    {
      title: t("task.list.title"),
      dataIndex: "Title",
      key: "Title",
      sorter: (a: any, b: any) => a.Title.localeCompare(b.Title),
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },
    {
      title: t("task.list.reporterUser"),
      dataIndex: "ReporterUserFullName",
      key: "Subject",
      sorter: (a: any, b: any) => a.Subject.localeCompare(b.Subject),
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },

    {
      title: t("task.list.priority"),
      dataIndex: "Priority",
      key: "Priority",
      sorter: (a: any, b: any) => a.Priority.localeCompare(b.Priority),
      render: (value: number) => {
        return (
          <>
            <Tag color="#0096d1" className="!text-xs">
              {determineTicketPriority(value, t)}
            </Tag>
          </>
        );
      },
    },
    {
      title: t("task.list.endDate"),
      dataIndex: "EndDate",
      key: "EndDate",

      render: (value: number) => {
        return (
          <>
            <Text className="!text-xs">
              {dayjs(value).format("YYYY-MM.DD HH:mm")}
            </Text>
          </>
        );
      },
    },
  ];

  return (
    <Row>
      <Col xs={24}>
        <span 
         onClick={()=>{
          navigate("/admin/tasks")
        }}
        className="!font-bold !text-sm cursor-pointer">{t("task.list.tasks")}</span>
      </Col>
      <Col xs={24}>
        <Table
          columns={columns}
          dataSource={task?.data?.Value}
          loading={task.isLoading || task.isFetching}
          pagination={{
            position: ["bottomRight"],
            className: "!px-0",

            total: task.data?.FilteredCount || 0,

            pageSize: 20,
            showLessItems: true,
            size: "small",
            showSizeChanger: true,
            locale: { items_per_page: "" },
            showTotal: (e) => `${e}`,
          }}
          rowKey={"Id"}
        />
      </Col>
    </Row>
  );
};

export default TaskSummaries;
