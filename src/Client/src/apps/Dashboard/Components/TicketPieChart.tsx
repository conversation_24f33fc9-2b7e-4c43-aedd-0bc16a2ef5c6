import { Card, Empty } from "antd";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { useGetDashboardSummaryInfoes } from "../ServerSideStates";
import { RootState } from "@/store/Reducers";
import { useNavigate } from "react-router-dom";

const data = [
  { name: "<PERSON><PERSON>", value: 15 },
  { name: "Devam Ediyor", value: 30 },
  { name: "<PERSON>pta<PERSON> Edildi", value: 10 },
  { name: "<PERSON><PERSON>land<PERSON>", value: 45 },
];


const COLORS = ["#34b115", "#e05b4a", "#ff9a0b", "#0096d1"]; // Yeni, Devam Ediyor, İ<PERSON><PERSON>, Tamamlandı

const TicketPieChart: React.FC = () => {
  const navigate = useNavigate()
  const { t } = useTranslation();
  const { filter } = useSelector((state: RootState) => state.dashboard);
  const summaryInfoes = useGetDashboardSummaryInfoes(filter);
  const [ticketStatusData, setTicketStatusData] = useState<any[]>([]);

  useEffect(() => {
    if (summaryInfoes?.data?.Value) {
      const taskCountByStatus = summaryInfoes?.data?.Value?.TaskCountByStatus      ;

      const result = taskCountByStatus
        ? Object.entries(taskCountByStatus).map(([name, value]) => ({
            name,
            value,
          }))
        : [];

      setTicketStatusData(result);
    }
  }, [summaryInfoes?.data]);
  return (
    <Card
      style={{ width: "100%", height: 400 }}
      bodyStyle={{ padding: "10px" }}
      loading={summaryInfoes?.isLoading||summaryInfoes?.isFetching}
    >
      <div>
        <span
         onClick={()=>{
          navigate("/admin/tickets")
        }}
        className="!font-bold !text-sm cursor-pointer">{t("dashboard.statusOfTickets")} </span>
      </div>
      {
        ticketStatusData?.length>0?<>
        
      <div>
        <ResponsiveContainer height={300}>
          <PieChart>
            <Pie
              data={ticketStatusData}
              cx="50%"
              cy="50%"
              outerRadius={100}
              label
              dataKey="value"
            >
              {data.map((entry, index) => (
                <Cell
                  key={`ticket-${index}`}
                  fill={COLORS[index % COLORS.length]}
                />
              ))}
            </Pie>
            <Tooltip />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </div>
        </>:<>
        <Empty/>
        </>
      }
    </Card>
  );
};

export default TicketPieChart;
