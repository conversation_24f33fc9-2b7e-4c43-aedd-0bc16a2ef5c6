import { Col, Row, Table, Tag, Typography } from "antd";

import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import dayjs from "dayjs";
import { determineAutoDialerStatus } from "@/helpers/AutoDialer";
import { useTranslation } from "react-i18next";

import { useGetAutoDialers } from "@/apps/Admin/Pages/AutoDialer/ServerSideStates";
import { useNavigate } from "react-router-dom";
const AutoDialerSummaries = () => {
  const { t } = useTranslation();
  const navigate = useNavigate()
  const { filter } = useSelector((state: RootState) => state.dashboard);
  const autoDialer = useGetAutoDialers({
    ...filter,
    PageNumber: 1,
    PageSize: 100,
    Status:1
  });
  const { Text } = Typography;

  const columns = [
    {
      title: t("autoDialer.list.name"),
      dataIndex: "Name",
      key: "name",
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
      sorter: (a: any, b: any) => {
        return a.Name.localeCompare(b.Name);
      },
    },
    {
      title: t("autoDialer.list.queueNumber"),
      dataIndex: "QueueNumber",
      key: "QueueNumber",
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },

    {
      title: t("autoDialer.list.startDate"),
      dataIndex: "StartDate",
      key: "StartDate",
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">
              {dayjs(value).format("YYYY-MM-DD HH:mm")}
            </Text>
          </>
        );
      },
    },
    {
      title: `${t("autoDialer.list.done")} / ${t("autoDialer.list.total")}`,

      render: (_: string, record: any) => {
        return (
          <>
           <Tag>
              {record?.DoneCount||0} / {record?.TotalCount||0}
            </Tag>
          </>
        );
      },
    },
    {
      title: t("autoDialer.list.status"),
      dataIndex: "Status",
      key: "Status",
      render: (value: number) => {
        return (
          <>
            <Tag
              className={`${determineAutoDialerStatus(
                "color",
                value,t
              )} !text-white`}
            >
              {determineAutoDialerStatus("value", value,t)}
            </Tag>
          </>
        );
      },
    },
  ];

  return (
    <Row>
      <Col xs={24}>
      <span   className="!font-bold !text-sm !cursor-pointer"
      onClick={()=>{
        navigate("/admin/auto-dailer")
      }}
      >{t("autoDialer.list.autoDialers")}</span>
      </Col>
      <Col xs={24}>
        <Table
          columns={columns}
          dataSource={autoDialer?.data?.Value}
          loading={autoDialer.isLoading || autoDialer.isFetching}
          pagination={{
            position: ["bottomRight"],
            className: "!px-0",

            total: autoDialer.data?.FilteredCount || 0,

            pageSize: 20,
            showLessItems: true,
            size: "small",
            showSizeChanger: true,
            locale: { items_per_page: "" },
            showTotal: (e) => `${e}`,
          }}
          rowKey={"Id"}
        />
      </Col>
    </Row>
  );
};

export default AutoDialerSummaries;
