import { Col, Divide<PERSON>, <PERSON> } from "antd";
import InfoCardList from "./Components/InfoCardList";
import AssignedPersonCallChart from "./Components/AssignedPersonCallChart";
import Title from "./Components/Title";
import TopFilters from "./Components/TopFilters";
import TaskSumamries from "./Components/TaskSummaries";
import AutoDialerSummaries from "./Components/AutodailerSummaries";
import PriorityPieChart from "./Components/PriorityPieChart";
import TicketPieChart from "./Components/TicketPieChart";
import { useQueryClient } from "react-query";
import { useEffect } from "react";
import endPoints from "./EndPoints";

const DashboardIndex = () => {
  const queryClient = useQueryClient();
  useEffect(() => {
    queryClient.resetQueries({
      queryKey: endPoints.getAllDashboardInfoes,
      exact: false,
    });
  }, []);

  return (
    <Col xs={24} className="!overflow-hidden">
      <Row gutter={[10, 10]}>
        <Col xs={24}>
          <Row>
            <Col xs={24}>
              <Title />
            </Col>
            <Divider className="!m-0" />
          </Row>
        </Col>
        <Col xs={24} className="!px-2">
          <TopFilters />
        </Col>
        <Col xs={24} md={10} className="!px-2">
          <InfoCardList />
        </Col>
        <Col xs={24} lg={14}>
          <Row gutter={[24, 24]}>
            <Col xs={24} md={12}>
              <PriorityPieChart />
            </Col>
            <Col xs={24} md={12}>
              <TicketPieChart />
            </Col>
          </Row>
        </Col>
        <Col xs={24} md={13} className="!px-2 !overflow-scroll">
          <AssignedPersonCallChart />
        </Col>
        <Col xs={24} md={11}>
          <TaskSumamries />
        </Col>
        <Col xs={24} className="!px-2">
          <AutoDialerSummaries />
        </Col>
      </Row>
    </Col>
  );
};
export default DashboardIndex;
