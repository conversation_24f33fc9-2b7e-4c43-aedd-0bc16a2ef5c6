using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Shared.Domain;

namespace General.Infrastructure.Services;

public class StorageService(
    ILogger<StorageService> logger
) : IStorageService
{
    private readonly ILogger<StorageService> _logger = logger;
    private readonly string _baseUploadPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Uploads");

    public async Task<(string fileName, string storagePath)> SaveFileAsync(IFormFile file, string folderPath, CancellationToken cancellationToken = default)
    {
        try
        {
            var fullFolderPath = Path.Combine(_baseUploadPath, folderPath);

            // Klasörün var olduğundan emin ol
            if (!Directory.Exists(fullFolderPath))
            {
                Directory.CreateDirectory(fullFolderPath);
            }

            var fileName = GetUniqueFileName(file.FileName, fullFolderPath);
            var filePath = Path.Combine(fullFolderPath, fileName);

            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream, cancellationToken);
            }

            var relativePath = Path.Combine(folderPath, fileName);
            return (fileName, relativePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Dosya kaydedilirken hata oluştu: {Message}", ex.Message);
            throw;
        }
    }

    public async Task<(string fileName, string storagePath)> SaveFileAsync(Stream fileStream, string originalFileName, string folderPath, CancellationToken cancellationToken = default)
    {
        try
        {
            var fullFolderPath = Path.Combine(_baseUploadPath, folderPath);

            // Klasörün var olduğundan emin ol
            if (!Directory.Exists(fullFolderPath))
            {
                Directory.CreateDirectory(fullFolderPath);
            }

            var fileName = GetUniqueFileName(originalFileName, fullFolderPath);
            var filePath = Path.Combine(fullFolderPath, fileName);

            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await fileStream.CopyToAsync(stream, cancellationToken);
            }

            var relativePath = Path.Combine(folderPath, fileName);
            return (fileName, relativePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Dosya kaydedilirken hata oluştu: {Message}", ex.Message);
            throw;
        }
    }

    public async Task<bool> DeleteFileAsync(string storagePath, CancellationToken cancellationToken = default)
    {
        try
        {
            var fullPath = Path.Combine(_baseUploadPath, storagePath);
            if (File.Exists(fullPath))
            {
                File.Delete(fullPath);
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Dosya silinirken hata oluştu: {Message}", ex.Message);
            return false;
        }
    }

    public async Task<Stream> GetFileStreamAsync(string storagePath, CancellationToken cancellationToken = default)
    {
        try
        {
            var fullPath = Path.Combine(_baseUploadPath, storagePath);
            if (!File.Exists(fullPath))
            {
                throw new FileNotFoundException($"Dosya bulunamadı: {storagePath}");
            }

            return new FileStream(fullPath, FileMode.Open, FileAccess.Read);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Dosya okunurken hata oluştu: {Message}", ex.Message);
            throw;
        }
    }

    public async Task<bool> CreateFolderAsync(string folderPath, CancellationToken cancellationToken = default)
    {
        try
        {
            var fullPath = Path.Combine(_baseUploadPath, folderPath);
            if (!Directory.Exists(fullPath))
            {
                Directory.CreateDirectory(fullPath);
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Klasör oluşturulurken hata oluştu: {Message}", ex.Message);
            return false;
        }
    }

    public async Task<bool> DeleteFolderAsync(string folderPath, bool recursive = false, CancellationToken cancellationToken = default)
    {
        try
        {
            var fullPath = Path.Combine(_baseUploadPath, folderPath);
            if (Directory.Exists(fullPath))
            {
                Directory.Delete(fullPath, recursive);
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Klasör silinirken hata oluştu: {Message}", ex.Message);
            return false;
        }
    }

    public async Task<bool> MoveFileAsync(string sourceStoragePath, string destinationStoragePath, CancellationToken cancellationToken = default)
    {
        try
        {
            var sourceFullPath = Path.Combine(_baseUploadPath, sourceStoragePath);
            var destinationFullPath = Path.Combine(_baseUploadPath, destinationStoragePath);

            if (!File.Exists(sourceFullPath))
            {
                return false;
            }

            var destinationDirectory = Path.GetDirectoryName(destinationFullPath);
            if (!Directory.Exists(destinationDirectory))
            {
                Directory.CreateDirectory(destinationDirectory);
            }

            File.Move(sourceFullPath, destinationFullPath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Dosya taşınırken hata oluştu: {Message}", ex.Message);
            return false;
        }
    }

    public async Task<bool> MoveFolderAsync(string sourceFolderPath, string destinationFolderPath, CancellationToken cancellationToken = default)
    {
        try
        {
            var sourceFullPath = Path.Combine(_baseUploadPath, sourceFolderPath);
            var destinationFullPath = Path.Combine(_baseUploadPath, destinationFolderPath);

            if (!Directory.Exists(sourceFullPath))
            {
                return false;
            }

            if (!Directory.Exists(destinationFullPath))
            {
                Directory.CreateDirectory(destinationFullPath);
            }

            // Klasördeki tüm dosya ve alt klasörleri taşı
            foreach (var file in Directory.GetFiles(sourceFullPath))
            {
                var fileName = Path.GetFileName(file);
                var destFile = Path.Combine(destinationFullPath, fileName);
                File.Move(file, destFile);
            }

            foreach (var directory in Directory.GetDirectories(sourceFullPath))
            {
                var dirName = Path.GetFileName(directory);
                var destDir = Path.Combine(destinationFullPath, dirName);
                await MoveFolderAsync(
                    Path.Combine(sourceFolderPath, dirName),
                    Path.Combine(destinationFolderPath, dirName),
                    cancellationToken);
            }

            // Kaynak klasörü sil
            Directory.Delete(sourceFullPath, false);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Klasör taşınırken hata oluştu: {Message}", ex.Message);
            return false;
        }
    }

    public string GetUniqueFileName(string originalFileName, string folderPath)
    {
        var fileName = Path.GetFileNameWithoutExtension(originalFileName);
        var extension = Path.GetExtension(originalFileName);
        var counter = 1;
        var newFileName = $"{fileName}{extension}";

        while (File.Exists(Path.Combine(folderPath, newFileName)))
        {
            newFileName = $"{fileName}({counter}){extension}";
            counter++;
        }

        return newFileName;
    }
}
