using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace General.Application.Files.UploadFile;

public class UploadFileEndpoint : IEndpoint
{
    public record UploadFileRequest(IFormFile File, Guid FolderId, Dictionary<string, object>? Metadata);
    public void MapEndpoint(IEndpointRouteBuilder endpoints)
    {
        endpoints.MapPost("/api/v1/general/files", async (
            [FromForm] UploadFileRequest request,
            ISender sender) =>
        {
            var command = new UploadFileCommand(request.File, request.FolderId, request.Metadata);
            var result = await sender.Send(command);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .DisableAntiforgery()
        .WithTags("General.Files")
        .WithGroupName("apiv1")
        .RequireAuthorization("General.Files");
    }
}
