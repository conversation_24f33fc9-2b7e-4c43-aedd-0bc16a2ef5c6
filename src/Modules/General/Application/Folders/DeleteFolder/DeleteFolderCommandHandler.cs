using General.Application.Abstractions;
using General.Domain;
using General.Infrastructure.Services;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Folders.DeleteFolder;

internal sealed class DeleteFolderCommandHandler(
    IGeneralDbContext dbContext,
    IStorageService storageService
) : IRequestHandler<DeleteFolderCommand, Result>
{
    private readonly IGeneralDbContext _dbContext = dbContext;
    private readonly IStorageService _storageService = storageService;

    public async Task<Result> Handle(DeleteFolderCommand request, CancellationToken cancellationToken)
    {
        var folder = await _dbContext.Folder
            .Include(f => f.Files)
            .Include(f => f.SubFolders)
            .FirstOrDefaultAsync(f => f.Id == request.Id, cancellationToken);

        if (folder == null)
        {
            return Result.Failure(GeneralErrors.FolderNotFound(request.Id));
        }

        // Recursive olmayan silme işleminde bağımlılıkları kontrol et
        if (!request.Recursive)
        {
            if (folder.Files.Any())
            {
                return Result.Failure(GeneralErrors.HasDependentFiles);
            }

            if (folder.SubFolders.Any())
            {
                return Result.Failure(GeneralErrors.HasDependentFolders);
            }
        }

        try
        {
            // Fiziksel klasörü sil
            var isDeleted = await _storageService.DeleteFolderAsync(
                folder.Path,
                request.Recursive,
                cancellationToken);

            if (!isDeleted)
            {
                return Result.Failure(GeneralErrors.FolderDeleteFailed("Fiziksel klasör silinemedi."));
            }

            // Event'i yayınla
            folder.Raise(new FolderDeletedEvent(folder.Id));

            // Recursive silme işleminde alt klasörleri ve dosyaları da sil
            if (request.Recursive)
            {
                // Önce dosyaları sil
                _dbContext.File.RemoveRange(folder.Files);

                // Alt klasörleri recursive olarak sil
                await DeleteSubFoldersRecursively(folder.Id, cancellationToken);
            }

            // Klasörü veritabanından sil
            _dbContext.Folder.Remove(folder);
            await _dbContext.SaveChangesAsync(cancellationToken);

            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure(GeneralErrors.FolderDeleteFailed(ex.Message));
        }
    }

    private async Task DeleteSubFoldersRecursively(Guid parentFolderId, CancellationToken cancellationToken)
    {
        var subFolders = await _dbContext.Folder
            .Include(f => f.Files)
            .Where(f => f.ParentFolderId == parentFolderId)
            .ToListAsync(cancellationToken);

        foreach (var subFolder in subFolders)
        {
            // Dosyaları sil
            _dbContext.File.RemoveRange(subFolder.Files);

            // Alt klasörleri recursive olarak sil
            await DeleteSubFoldersRecursively(subFolder.Id, cancellationToken);

            // Klasörü sil
            _dbContext.Folder.Remove(subFolder);
        }
    }
}
