using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace General.Application.Countries.GetCountries;

internal sealed class GetCountriesEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder endpoints)
    {
        endpoints.MapGet("/api/v1/general/countries", async (
            bool? isActive,
            int? pageNumber,
            int? pageSize,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new GetCountriesQuery(
                isActive,
                pageNumber ?? 1,
                pageSize ?? 20);
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("General.Countries")
        .WithGroupName("apiv1")
        .RequireAuthorization("General.Countries");
    }
}
