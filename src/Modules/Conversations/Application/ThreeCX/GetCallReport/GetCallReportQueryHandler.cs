using Conversations.Application.Abstractions;
using MediatR;
using Shared.Application;
using Shared.Contracts;

namespace Conversations.Application.ThreeCX.GetCallReport;

public class GetCallReportQueryHandler(
    IThreeCXService threeCXService,
    ISharedCustomerService customerService,
    ISharedUserService userService
) : IRequestHandler<GetCallReportQuery, Result<List<CallReportResponse>>>
{
    private readonly IThreeCXService _threeCXService = threeCXService;
    private readonly ISharedCustomerService _customerService = customerService;
    private readonly ISharedUserService _userService = userService;

    public async Task<Result<List<CallReportResponse>>> Handle(GetCallReportQuery request, CancellationToken cancellationToken)
    {
        var result = await _threeCXService.GetCallReport(
            request.ExtensionNumbers,
            request.StartDate,
            request.EndDate,
            request.Direction,
            request.IsAnswered,
            request.Caller,
            request.Callee,
            request.PageNumber,
            request.PageSize);
        if (!result.IsSuccess)
        {
            return Result.Failure<List<CallReportResponse>>(result.Error.Code, result.Error.Description);
        }
        var response = result.Value.Select(x => new CallReportResponse
        {
            HistoryOfTheCall = x.HistoryOfTheCall,
            Caller = x.Caller,
            Callee = x.Callee,
            Direction = x.Direction,
            StartTime = x.StartTime,
            AnsweredTime = x.AnsweredTime,
            EndTime = x.EndTime,
            TalkDurationInSeconds = x.TalkDurationInSeconds,
            TotalDurationInSeconds = x.TotalDurationInSeconds,
            IsAnswered = x.IsAnswered,
            Extension = x.Extension,
            RecordingUrl = x.RecordingUrl,
            Transcription = x.Transcription
        }).ToList();
        var callers = response.Select(x => x.Caller ?? "").Distinct().ToList();
        var callees = response.Select(x => x.Callee ?? "").Distinct().ToList();
        var list = callers.Concat(callees ?? []);
        var users = (await _userService.GetUsersByExtensionsAsync([.. list])).ToDictionary(x => x.Extension, x => x.Name + " " + x.Surname);
        var customers = (await _customerService.GetCustomersByPhonesAsync([.. list])).Value.ToDictionary(x => x.Phone, x => x.Name + " " + x.Surname);
        response.ForEach(x =>
        {
            x.CalleeName = customers.TryGetValue(x.Callee ?? "", out var calleename) ? calleename : users.TryGetValue(x.Callee ?? "", out var calleename2) ? calleename2 : "Bilinmeyen";
            x.CallerName = customers.TryGetValue(x.Caller ?? "", out var callername) ? callername : users.TryGetValue(x.Caller ?? "", out var callername2) ? callername2 : "Bilinmeyen";
        });
        return Result.Success(response);
    }
}
