using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Sectors.GetSectorsList;

public class GetSectorsListQueryHandler : IRequestHandler<GetSectorsListQuery, PagedResult<SectorDto>>
{
    private readonly ICustomersDbContext _dbContext;

    public GetSectorsListQueryHandler(ICustomersDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<PagedResult<SectorDto>> Handle(GetSectorsListQuery request, CancellationToken cancellationToken)
    {
        var query = _dbContext.Sector.AsQueryable();

        // Apply search filter if provided
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.Trim().ToLower();
            query = query.Where(p => p.Name.ToLower().Contains(searchTerm));
        }

        // Get total count
        var totalCount = await _dbContext.Sector.CountAsync(cancellationToken);
        var filteredCount = await query.CountAsync(cancellationToken);

        // Apply pagination
        var sectors = await query
            .OrderBy(p => p.Name)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(p => new SectorDto(
                p.Id,
                p.Name))
            .ToListAsync(cancellationToken);

        var result = PagedResult<SectorDto>.Success(sectors);
        result.PageNumber = request.PageNumber;
        result.PageSize = request.PageSize;
        result.Count = totalCount;
        result.FilteredCount = filteredCount;

        return result;
    }
}
