using Customers.Domain;
namespace Customers.Application.TempCustomers.ExportTempCustomer;
public class TempCustomerFilterRequest
{
    public string? Name { get; set; }
    public string? Surname { get; set; }
    public string? Email { get; set; }
    public string? Phone { get; set; }
    public string? Country { get; set; }
    public string? MainLanguage { get; set; }
    public string? AvailableLanguage { get; set; }
    public string? Description { get; set; }
    public string? MailBcc { get; set; }
    public string? TaxOffice { get; set; }
    public string? TaxNumber { get; set; }
    public string? IdentificationNumber { get; set; }

    public CustomerType? Type { get; set; }
    public CustomerKind? Kind { get; set; }
    public CustomerStatus? Status { get; set; }

    public Guid? ClassificationId { get; set; }
    public Guid? CustomerSourceId { get; set; }
    public Guid? AdvisorId { get; set; }

    public bool? IsDeleted { get; set; }
}
