using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Shared.Application;

namespace Customers.Application.Customers.CreateCustomer;

internal sealed class CreateCustomerCommandHandler(
    ICustomersDbContext context
) : IRequestHandler<CreateCustomerCommand, Result<Guid>>
{
    public async Task<Result<Guid>> Handle(CreateCustomerCommand request, CancellationToken cancellationToken)
    {
        var customer = Customer.Create(
            request.Name,
            request.Surname,
            request.Email,
            request.Phone,
            request.PhonePrefix,
            request.Type);

        customer.TaxOffice = request.TaxOffice;
        customer.TaxNumber = request.TaxNumber;
        customer.IdentificationNumber = request.IdentificationNumber;
        customer.Country = request.Country;
        customer.MainLanguage = request.MainLanguage;
        customer.AvailableLanguage = request.AvailableLanguage;
        customer.Description = request.Description;
        customer.MailBcc = request.MailBcc;
        customer.Kind = request.Kind;
        customer.Status = request.Status;
        customer.CustomerSourceId = request.CustomerSourceId;
        customer.NotificationWayId = request.NotificationWayId;
        customer.ProfessionId = request.ProfessionId;
        customer.SectorId = request.SectorId;
        customer.AdvisorId = request.AdvisorId;
        customer.AttributeData = request.AttributeData;
        if (request.ClassificationIds != null && request.ClassificationIds.Any())
        {
            customer.UpdateClassifications(request.ClassificationIds);
        }
        context.Customers.Add(customer);
        await context.SaveChangesAsync(cancellationToken);
        return Result.Success(customer.Id);
    }
}
