using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Customers.UpdateCustomer;

internal sealed class UpdateCustomerCommandHandler(
    ICustomersDbContext context
) : IRequestHandler<UpdateCustomerCommand, Result>
{
    public async Task<Result> Handle(UpdateCustomerCommand request, CancellationToken cancellationToken)
    {
        var customer = await context.Customers
            .Include(x => x.CustomerClassifications)
            .FirstOrDefaultAsync(x => x.Id == request.Id && !x.IsDeleted, cancellationToken);
        if (customer is null)
        {
            return Result.Failure(CustomerErrors.CustomerNotFound(request.Id));
        }
        customer.UpdateBasicInfo(
            request.Name,
            request.Surname,
            request.Email,
            request.Phone,
            request.PhonePrefix,
            request.Type);
        customer.TaxOffice = request.TaxOffice;
        customer.TaxNumber = request.TaxNumber;
        customer.IdentificationNumber = request.IdentificationNumber;
        customer.Country = request.Country;
        customer.MainLanguage = request.MainLanguage;
        customer.AvailableLanguage = request.AvailableLanguage;
        customer.Description = request.Description;
        customer.MailBcc = request.MailBcc;
        customer.Kind = request.Kind;
        customer.Status = request.Status;
        customer.CustomerSourceId = request.CustomerSourceId;
        customer.NotificationWayId = request.NotificationWayId;
        customer.ProfessionId = request.ProfessionId;
        customer.SectorId = request.SectorId;
        customer.AdvisorId = request.AdvisorId;
        customer.AttributeData = request.AttributeData;
        if (request.ClassificationIds != null && request.ClassificationIds.Any())
        {
            customer.UpdateClassifications(request.ClassificationIds);
        }

        await context.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
