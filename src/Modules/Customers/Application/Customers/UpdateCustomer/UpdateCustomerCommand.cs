using Customers.Domain;
using MediatR;
using Shared.Application;

namespace Customers.Application.Customers.UpdateCustomer;

public record UpdateCustomerCommand(
    Guid Id,
    string Name,
    string? Surname,
    string? Email,
    string Phone,
    string PhonePrefix,
    string TaxOffice,
    string TaxNumber,
    string IdentificationNumber,
    string Country,
    string MainLanguage,
    string AvailableLanguage,
    string Description,
    string MailBcc,
    CustomerType Type,
    CustomerKind? Kind,
    CustomerStatus? Status,
    Guid? CustomerSourceId,
    Guid? NotificationWayId,
    List<Guid>? ClassificationIds,
    Guid? SectorId,
    Guid? ProfessionId,
    Guid? AdvisorId,
    Dictionary<string, string>? AttributeData
) : IRequest<Result>;
