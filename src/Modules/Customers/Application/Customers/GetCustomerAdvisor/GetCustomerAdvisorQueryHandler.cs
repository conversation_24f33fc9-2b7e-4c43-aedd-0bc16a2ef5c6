using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Contracts;

namespace Customers.Application.Customers.GetCustomerAdvisor;

internal sealed class GetCustomerAdvisorQueryHandler(
    ICustomersDbContext context,
    ISharedUserService userService
) : IRequestHandler<GetCustomerAdvisorQuery, Result<SharedUserDTO>>
{
    private readonly ICustomersDbContext _context = context;
    private readonly ISharedUserService _userService = userService;

    public async Task<Result<SharedUserDTO>> Handle(GetCustomerAdvisorQuery request, CancellationToken cancellationToken)
    {
        var cleanphone = request.Phone.TrimStart('0');
        var customer = await _context.Customers
            .FirstOrDefaultAsync(x => (x.PhonePrefix + x.Phone).Contains(cleanphone) && !x.IsDeleted, cancellationToken);

        if (customer is null)
        {
            return Result.Failure<SharedUserDTO>(CustomerErrors.CustomerNotFound(request.Phone));
        }
        if (customer.AdvisorId == null)
        {
            return Result.Failure<SharedUserDTO>("Müşteriye atan bir danışman yok.");
        }
        var userResult = await _userService.GetUserAsync(customer.AdvisorId.Value);
        if (!userResult.IsSuccess)
        {
            return Result.Failure<SharedUserDTO>(userResult.Error);
        }
        return Result.Success(userResult.Value);
    }
}
