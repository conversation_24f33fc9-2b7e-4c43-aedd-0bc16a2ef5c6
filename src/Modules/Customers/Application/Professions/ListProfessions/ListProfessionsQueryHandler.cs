using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Professions.ListProfessions;

public class ListProfessionsQueryHandler : IRequestHandler<ListProfessionsQuery, PagedResult<ProfessionDto>>
{
    private readonly ICustomersDbContext _dbContext;

    public ListProfessionsQueryHandler(ICustomersDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<PagedResult<ProfessionDto>> Handle(ListProfessionsQuery request, CancellationToken cancellationToken)
    {
        var query = _dbContext.Profession.AsQueryable();

        // Apply search filter if provided
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.Trim().ToLower();
            query = query.Where(p => p.Name.ToLower().Contains(searchTerm));
        }

        // Get total count
        var totalCount = await _dbContext.Profession.CountAsync(cancellationToken);
        var filteredCount = await query.CountAsync(cancellationToken);

        // Apply pagination
        var professions = await query
            .OrderBy(p => p.Name)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(p => new ProfessionDto(
                p.Id,
                p.Name))
            .ToListAsync(cancellationToken);

        var result = PagedResult<ProfessionDto>.Success(professions);
        result.PageNumber = request.PageNumber;
        result.PageSize = request.PageSize;
        result.Count = totalCount;
        result.FilteredCount = filteredCount;

        return result;
    }
}
