using MediatR;
using Shared.Application;
using Users.Application.Abstractions;
using Users.Domain.Account;
using Shared.Infrastructure.Excel;
using Microsoft.EntityFrameworkCore;
using Shared.Infrastructure.Localization;
using Azure.Core;
using Microsoft.AspNetCore.Identity;
using Users.Domain.Account.Events;
using Users.Infrastructure.Data;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Text;
using static Shared.Application.Validation.RuleBuilderExtensions;
using System.Text.RegularExpressions;


namespace Users.Application.Management.ImportUser;

public class ImportUserCommandHandler : IRequestHandler<ImportUserCommand, Result<ImportUserCommandResponse>>
{
    private readonly IUserDbContext _dbContext;
    private readonly ExcelHelper _excelHelper;
    private readonly ILocalizer _localizer;
    private readonly UserManager<User> _userManager;
    private readonly RoleManager<Role> _roleManager;
    private readonly IWorkContext _workContext;
    private readonly ILogger<ImportUserCommandHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public ImportUserCommandHandler(
        IWorkContext workContext,
        IUserDbContext dbContext,
        ExcelHelper excelHelper,
        ILocalizer localizer,
        RoleManager<Role> roleManager,
        UserManager<User> userManager,
        ILogger<ImportUserCommandHandler> logger,
        IHttpContextAccessor httpContextAccessor)
    {
        _dbContext = dbContext;
        _excelHelper = excelHelper;
        _localizer = localizer;
        _userManager = userManager;
        _roleManager = roleManager;
        _workContext = workContext;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }
    //User'lara ait Extention numara 3cx 'de var mı yok mu yok ise oluşturma işlemi de en son aşamada yapılacak.
    //Eklenen tüm Extentionlar alınacak 3xc 'den sorgulanacak yok ise oluşturulacak.
 public async Task<Result<ImportUserCommandResponse>> Handle(ImportUserCommand request, CancellationToken cancellationToken)
    {
        ImportUserCommandResponse response = new();
        var validationErrors = new List<string>();
        try
        {
            var excelData = _excelHelper.ReadExcelDataWithMapping(request.TempFileName, request.SheetName);

            if (excelData == null || !excelData.Any())
            {
                validationErrors.Add(_localizer.Get("ExcelReadError"));
                var errors = validationErrors.Select(err => new Error("ValidationError", err, ErrorType.Validation)).ToArray();
                return Result<ImportUserCommandResponse>.Validation(new ValidationError(errors), response);
            }

            var mappedDtos = ImportUserExcelMapper.Map(excelData, request.Mappings, _localizer);

            if (!mappedDtos.Any())
            {
                validationErrors.Add(_localizer.Get("InvalidExcelData"));
                var errors = validationErrors.Select(err => new Error("ValidationError", err, ErrorType.Validation)).ToArray();
                return Result<ImportUserCommandResponse>.Validation(new ValidationError(errors), response);
            }

            var validator = new ImportUserExcelDtoValidator(_localizer);

            foreach (var dto in mappedDtos)
            {
                if (!string.IsNullOrWhiteSpace(dto.Phone))
                {
                    if (PhoneNormalizer.TryNormalize(dto.Phone, out var normalized, out var prefix))
                    {
                        (dto.Phone, dto.PhonePrefix) = PhoneNormalizer.ApplyRegionalRules(normalized, prefix);
                    }
                }

                dto.DepartmanName ??= _localizer.Get("DefaultRole");
                dto.Group = dto.Group?.Trim() ?? "User";
                dto.ExtentionNo = dto.ExtentionNo?.Trim();
                dto.Password ??= GenerateRandomPassword();
                dto.Active ??= true;
            }

            var validDtos = new List<ImportUserExcelDto>();
            foreach (var dto in mappedDtos)
            {
                var validationResult = validator.Validate(dto);
                if (!validationResult.IsValid)
                {
                    foreach (var error in validationResult.Errors)
                    {
                        validationErrors.Add(_localizer.Get("Import.PropertyValidationError", new
                        {
                            Row = dto.RowNumber,
                            Property = error.PropertyName,
                            Detail = error.ErrorMessage
                        }));
                    }
                }
                else
                {
                    validDtos.Add(dto);
                }
            }

            var requestedEmails = validDtos
                .Select(x => x.Email?.Trim().ToLowerInvariant())
                .Where(x => !string.IsNullOrWhiteSpace(x))
                .Distinct()
                .ToList();

            var requestedExtensions = validDtos
                .Select(x => x.ExtentionNo?.Trim())
                .Where(x => !string.IsNullOrWhiteSpace(x))
                .Distinct()
                .ToList();

            var allDbEmails = await _dbContext.Users.IgnoreQueryFilters()
                .Select(u => new { u.Email, u.ThreeCXExtension, u.IsDeleted })
                .ToListAsync(cancellationToken);

            var existingUsers = allDbEmails
                .Where(x => requestedEmails.Contains(x.Email.ToLowerInvariant()))
                .ToList();

            var reactivatedUsers = new HashSet<string>(existingUsers
                .Where(x => x.IsDeleted)
                .Select(x => x.Email.ToLowerInvariant()));

            var usedEmails = new HashSet<string>(existingUsers
                .Where(x => !x.IsDeleted)
                .Select(x => x.Email.ToLowerInvariant()));

            var usedExtensions = new HashSet<string>(existingUsers
                .Where(x => !string.IsNullOrWhiteSpace(x.ThreeCXExtension))
                .Select(x => x.ThreeCXExtension!));

            var duplicateEmails = validDtos
                .GroupBy(x => x.Email?.Trim().ToLowerInvariant())
                .Where(g => g.Count() > 1)
                .Select(g => g.Key)
                .ToHashSet();

            var finalValidDtos = validDtos
        .Where(dto =>
            !usedEmails.Contains(dto.Email.ToLowerInvariant()) &&
            !duplicateEmails.Contains(dto.Email.ToLowerInvariant()) &&
            !reactivatedUsers.Contains(dto.Email.ToLowerInvariant()) && // <-- Burası önemli
            (string.IsNullOrWhiteSpace(dto.ExtentionNo) || !usedExtensions.Contains(dto.ExtentionNo)))
        .ToList();

            var finalValidDtoEmails = new HashSet<string>(finalValidDtos.Select(x => x.Email.ToLowerInvariant()));

            foreach (var dto in validDtos.Where(dto => !finalValidDtoEmails.Contains(dto.Email.ToLowerInvariant())))
            {
                validationErrors.Add(_localizer.Get("UserImport.AlreadyExists", new
                {
                    Row = dto.RowNumber,
                    Email = dto.Email
                }));
            }

            foreach (var dto in validDtos.Where(dto => usedExtensions.Contains(dto.ExtentionNo)))
            {
                validationErrors.Add(_localizer.Get("UserImport.ExtensionAlreadyExists", new
                {
                    Row = dto.RowNumber,
                    Extension = dto.ExtentionNo
                }));
            }

            if (!finalValidDtos.Any() && !reactivatedUsers.Any())
            {
                var errors = validationErrors.Select(err => new Error("ValidationError", err, ErrorType.Validation)).ToArray();
                return Result<ImportUserCommandResponse>.Validation(new ValidationError(errors), response);
            }


            var reactivatedUserEmails = new HashSet<string>(reactivatedUsers);

            foreach (var dto in validDtos.Where(dto => reactivatedUserEmails.Contains(dto.Email.ToLowerInvariant())))
            {
                var normalizedEmail = dto.Email.ToLowerInvariant();

                var user = await _dbContext.Users
                    .IgnoreQueryFilters()
                    .FirstOrDefaultAsync(x => x.Email.ToLower() == normalizedEmail, cancellationToken);

                if (user != null)
                {
                    user.IsDeleted = false;
                    user.Active = dto.Active ?? true;
                    user.Name = dto.Name;
                    user.Surname = dto.Surname;
                    user.PhoneNumber = dto.Phone;
                    user.ThreeCXExtension = dto.ExtentionNo;
                    user.InsertDate = DateTime.UtcNow;
                    user.InsertUserId = _workContext.UserId;
                }
            }

            // Departman işlemleri
            var existingDepartments = await _dbContext.Department.AsNoTracking().ToListAsync(cancellationToken);
            var departmentDict = existingDepartments
                .GroupBy(d => d.Name.Trim().ToLower())
                .ToDictionary(g => g.Key, g => g.First());

            var distinctDeptNames = finalValidDtos.Select(x => x.DepartmanName?.Trim())
                                                 .Where(x => !string.IsNullOrWhiteSpace(x))
                                                 .Distinct(StringComparer.OrdinalIgnoreCase)
                                                 .ToList();

            var newDepts = new List<Department>();
            foreach (var deptName in distinctDeptNames)
            {
                var key = deptName.ToLower();
                if (!departmentDict.ContainsKey(key))
                {
                    var newDept = new Department { Name = deptName, Description = string.Empty };
                    newDepts.Add(newDept);
                    departmentDict[key] = newDept;
                }
            }
            if (newDepts.Any())
            {
                await _dbContext.Department.AddRangeAsync(newDepts, cancellationToken);
                await _dbContext.SaveChangesAsync(cancellationToken);
            }

            // Rol işlemleri
            var existingRoles = await _roleManager.Roles.Select(r => r.Name).ToListAsync(cancellationToken);
            var roleSet = new HashSet<string>(existingRoles, StringComparer.OrdinalIgnoreCase);

            var requiredRoles = finalValidDtos.Select(dto => dto.Group?.Trim())
                                             .Where(role => !string.IsNullOrWhiteSpace(role))
                                             .Distinct(StringComparer.OrdinalIgnoreCase)
                                             .Where(role => !roleSet.Contains(role!))
                                             .ToList();

            foreach (var roleName in requiredRoles)
            {
                var result = await _roleManager.CreateAsync(new Role
                {
                    Name = roleName,
                    NormalizedName = roleName.ToUpper()
                });
                if (result.Succeeded)
                    roleSet.Add(roleName);
            }

            // Yeni kullanıcı ekleme
            var newUsers = new List<User>();
            foreach (var dto in finalValidDtos)
            {
                var deptKey = dto.DepartmanName?.Trim().ToLower();
                departmentDict.TryGetValue(deptKey ?? string.Empty, out var department);

                var user = new User
                {
                    UserName = dto.Email,
                    Email = dto.Email,
                    Name = dto.Name,
                    Surname = dto.Surname,
                    PhoneNumber = dto.Phone,
                    Active = dto.Active ?? true,
                    InsertDate = DateTime.UtcNow,
                    InsertUserId = _workContext.UserId,
                    ThreeCXExtension = dto.ExtentionNo
                };

                var result = await _userManager.CreateAsync(user, dto.Password);
                if (result.Succeeded)
                {
                    user.Raise(new UserCreatedDomainEvent(user.Id));
                    newUsers.Add(user);

                    if (department != null)
                    {
                        await _dbContext.UserDepartment.AddAsync(new UserDepartment
                        {
                            UserId = user.Id,
                            DepartmentId = department.Id
                        }, cancellationToken);
                    }
                }
                else
                {
                    foreach (var error in result.Errors)
                    {
                        validationErrors.Add(_localizer.Get("UserImport.ValidationError", new { Row = dto.RowNumber, Detail = error.Description }));
                    }
                }
            }

            await _dbContext.SaveChangesAsync(cancellationToken);

            // Rol atamaları
            foreach (var user in newUsers)
            {
                var roleName = finalValidDtos.FirstOrDefault(x => x.Email == user.Email)?.Group?.Trim() ?? "User";

                if (!string.IsNullOrWhiteSpace(roleName) && roleSet.Contains(roleName))
                {
                    await _userManager.AddToRoleAsync(user, roleName);
                }
            }

            // Loglama
            var ip = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString();
            var userAgent = _httpContextAccessor.HttpContext?.Request?.Headers["User-Agent"].ToString();

            _logger.LogInformation(
                "User import completed by UserId: {UserId}. Total: {Total}, Success: {Success}, Failed: {Failed}, File: {FileName}, IP: {IP}, UA: {UserAgent}, Date: {Date}",
                _workContext.UserId,
                mappedDtos.Count,
                finalValidDtos.Count,
                mappedDtos.Count - finalValidDtos.Count,
                request.TempFileName,
                ip,
                userAgent,
                DateTime.UtcNow);

            response.TotalRecords = mappedDtos.Count;
            response.SuccessfulRecords = finalValidDtos.Count;
            response.Errors = validationErrors;
            response.Success = !validationErrors.Any();

            var resultErrors = validationErrors.Select(err => new Error("ValidationError", err, ErrorType.Validation)).ToArray();
            if (resultErrors.Any())
                return Result<ImportUserCommandResponse>.Validation(new ValidationError(resultErrors), response);

            return Result.Success(response);
        }
        catch (FormatException ex)
        {
            var errors = new[]
            {
            new Error("ExcelMappingError", ex.Message, ErrorType.Validation)
        };

            return Result<ImportUserCommandResponse>.Validation(new ValidationError(errors), response);
        }
        catch (DbUpdateException ex) when (ex.InnerException?.Message.Contains("IX_User_Email") == true)
        {
            var conflictEmail = Regex.Match(ex.InnerException.Message, @"\((.*?)\)").Groups[1].Value;
            var errors = new[]
            {
            new Error("UniqueConstraint", _localizer.Get("UserImport.AlreadyExists", new { Email = conflictEmail }), ErrorType.Validation)
        };
            return Result<ImportUserCommandResponse>.Validation(new ValidationError(errors), response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "User import failed with unhandled exception");
            return Result.Failure<ImportUserCommandResponse>(_localizer.Get("UnexpectedError") + " " + ex.Message);
        }
    }

private static string GenerateRandomPassword(int length = 10)
{
    if (length < 4)
        throw new ArgumentException("Password length must be at least 4.");

    const string upper = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const string lower = "abcdefghijklmnopqrstuvwxyz";
    const string digits = "0123456789";
    const string symbols = "!@#$%^&*()";
    const string allChars = upper + lower + digits + symbols;

    using var rng = RandomNumberGenerator.Create();
    var password = new char[length];

    password[0] = GetRandomChar(upper, rng);
    password[1] = GetRandomChar(lower, rng);
    password[2] = GetRandomChar(digits, rng);
    password[3] = GetRandomChar(symbols, rng);

    for (int i = 4; i < length; i++)
        password[i] = GetRandomChar(allChars, rng);

    return Shuffle(password, rng);
}

private static char GetRandomChar(string chars, RandomNumberGenerator rng)
{
    var data = new byte[1];
    do
    {
        rng.GetBytes(data);
    } while (data[0] >= (byte.MaxValue - (byte.MaxValue % chars.Length))); // unbiased mod

    return chars[data[0] % chars.Length];
}

private static string Shuffle(char[] array, RandomNumberGenerator rng)
{
    for (int i = array.Length - 1; i > 0; i--)
    {
        var buffer = new byte[1];
        rng.GetBytes(buffer);
        int j = buffer[0] % (i + 1);
        (array[i], array[j]) = (array[j], array[i]);
    }
    return new string(array);
}
}
