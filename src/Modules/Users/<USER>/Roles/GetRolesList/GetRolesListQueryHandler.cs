using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Users.Application.Abstractions;
using Users.Domain.Account;

namespace Users.Application.Roles.GetRolesList;

public class GetRolesListQueryHandler(
    RoleManager<Role> roleManager,
    IUserDbContext dbContext
) : IRequestHandler<GetRolesListQuery, PagedResult<RoleListDto>>
{
    private readonly RoleManager<Role> _roleManager = roleManager;
    private readonly IUserDbContext _dbContext = dbContext;

    public async Task<PagedResult<RoleListDto>> Handle(GetRolesListQuery request, CancellationToken cancellationToken)
    {
        var excludeRoles = new List<string>() { "User", "Admin" };
        var query = _roleManager.Roles.Where(x => !excludeRoles.Contains(x.Name)).AsQueryable();
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            query = query.Where(r => r.Name != null && r.Name.ToLower().Contains(searchTerm));
        }
        var totalCount = await _roleManager.Roles.CountAsync(cancellationToken);
        var filteredCount = await query.CountAsync(cancellationToken);
        var userCounts = await _dbContext.UserRoles
            .GroupBy(ur => ur.RoleId)
            .Select(g => new { RoleId = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.RoleId, x => x.Count, cancellationToken);
        var roles = await query
            .OrderBy(r => r.Name)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToListAsync(cancellationToken);
        var roleDtos = roles.Select(r => new RoleListDto
        {
            Id = r.Id,
            Name = r.Name ?? "",
            UserCount = userCounts.TryGetValue(r.Id, out int value) ? value : 0,
            IsSystemRole = r.Id == Role.ADMIN || r.Id == Role.USER
        }).ToList();

        var result = PagedResult<RoleListDto>.Success(roleDtos);
        result.PageNumber = request.PageNumber;
        result.PageSize = request.PageSize;
        result.Count = totalCount;
        result.FilteredCount = filteredCount;

        return result;
    }
}
