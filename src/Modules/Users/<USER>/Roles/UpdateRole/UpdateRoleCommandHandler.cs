using MediatR;
using Microsoft.AspNetCore.Identity;
using Shared.Application;
using Users.Domain.Account;
using Users.Domain.Account.Events;

namespace Users.Application.Roles.UpdateRole;

public class UpdateRoleCommandHandler(
    RoleManager<Role> roleManager
) : IRequestHandler<UpdateRoleCommand, Result>
{
    private readonly RoleManager<Role> _roleManager = roleManager;

    public async Task<Result> Handle(UpdateRoleCommand request, CancellationToken cancellationToken)
    {
        var role = await _roleManager.FindByIdAsync(request.Id.ToString());

        if (role == null)
        {
            return Result.Failure("Rol bulunamadı.");
        }

        // Admin ve User rollerini güncellemeye izin verme
        if (role.Id == Role.ADMIN || role.Id == Role.USER)
        {
            return Result.Failure("Sistem rolleri güncellenemez.");
        }

        role.Name = request.Name;
        role.NormalizedName = request.Name.ToUpper();

        var result = await _roleManager.UpdateAsync(role);

        if (!result.Succeeded)
        {
            return Result.Failure(new ValidationError(
                result.Errors.Select(e => Error.Problem(e.Code, e.Description)).ToArray()));
        }

        // Domain event oluştur
        role.Raise(new RoleUpdatedDomainEvent(role.Id));

        return Result.Success();
    }
}
