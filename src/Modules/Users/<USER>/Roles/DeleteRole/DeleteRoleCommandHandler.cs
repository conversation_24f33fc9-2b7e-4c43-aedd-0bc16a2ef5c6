using MediatR;
using Microsoft.AspNetCore.Identity;
using Shared.Application;
using Users.Domain.Account;
using Users.Domain.Account.Events;

namespace Users.Application.Roles.DeleteRole;

public class DeleteRoleCommandHandler(
    RoleManager<Role> roleManager
) : IRequestHandler<DeleteRoleCommand, Result>
{
    private readonly RoleManager<Role> _roleManager = roleManager;

    public async Task<Result> Handle(DeleteRoleCommand request, CancellationToken cancellationToken)
    {
        var role = await _roleManager.FindByIdAsync(request.Id.ToString());

        if (role == null)
        {
            return Result.Failure("Rol bulunamadı.");
        }

        // Admin ve User rollerini silmeye izin verme
        if (role.Id == Role.ADMIN || role.Id == Role.USER)
        {
            return Result.Failure("Sistem rolleri silinemez.");
        }

        var result = await _roleManager.DeleteAsync(role);

        if (!result.Succeeded)
        {
            return Result.Failure(new ValidationError(
                result.Errors.Select(e => Error.Problem(e.Code, e.Description)).ToArray()));
        }

        // Domain event oluştur
        role.Raise(new RoleDeletedDomainEvent(role.Id));

        return Result.Success();
    }
}
