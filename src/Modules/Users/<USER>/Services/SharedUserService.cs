using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Contracts;
using Users.Application.Abstractions;

namespace Users.Application.Services;

public class SharedUserService(
    IUserDbContext dbContext
) : ISharedUserService
{
    private readonly IUserDbContext _dbContext = dbContext;


    public async Task<Result<SharedUserDTO>> GetUserAsync(Guid userId)
    {
        var user = await _dbContext.Users
            .Include(x => x.UserDepartment).FirstAsync(x => x.Id == userId);
        if (user == null)
        {
            return Result.Failure<SharedUserDTO>("404", "User not found");
        }
        return new SharedUserDTO
        {
            Id = user.Id,
            Active = user.Active,
            Name = user.Name,
            Surname = user.Surname,
            UserName = user.UserName,
            Email = user.Email,
            PhoneNumber = user.PhoneNumber,
            Extension = user.ThreeCXExtension,
            DepartmentIds = user.UserDepartment.Select(x => x.DepartmentId).ToList()
        };
    }

    public async Task<Result<SharedUserDTO>> GetUserAsync(string extension)
    {
        var user = await _dbContext.Users.FirstOrDefaultAsync(x => x.ThreeCXExtension == extension);
        if (user == null)
        {
            return Result.Failure<SharedUserDTO>("404", "User not found");
        }
        return new SharedUserDTO
        {
            Id = user.Id,
            Active = user.Active,
            Name = user.Name,
            Surname = user.Surname,
            UserName = user.UserName,
            Email = user.Email,
            PhoneNumber = user.PhoneNumber,
            Extension = user.ThreeCXExtension
        };
    }

    public async Task<List<SharedUserDTO>> GetUsersByExtensionsAsync(List<string> extensions)
    {
        var users = await _dbContext.Users
            .Where(u => extensions.Contains(u.ThreeCXExtension))
            .ToListAsync();

        return users.Select(user => new SharedUserDTO
        {
            Id = user.Id,
            Active = user.Active,
            Name = user.Name,
            Surname = user.Surname,
            UserName = user.UserName,
            Email = user.Email,
            PhoneNumber = user.PhoneNumber,
            Extension = user.ThreeCXExtension
        }).ToList();
    }

    public async Task<List<SharedUserDTO>> GetUsersByIdsAsync(List<Guid> userIds)
    {
        var users = await _dbContext.Users
            .Where(u => userIds.Contains(u.Id))
            .ToListAsync();

        return users.Select(user => new SharedUserDTO
        {
            Id = user.Id,
            Active = user.Active,
            Name = user.Name,
            Surname = user.Surname,
            UserName = user.UserName,
            Email = user.Email,
            PhoneNumber = user.PhoneNumber,
            Extension = user.ThreeCXExtension
        }).ToList();
    }
}
