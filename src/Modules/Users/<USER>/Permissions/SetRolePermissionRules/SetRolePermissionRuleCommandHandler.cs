using System.Linq;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Users.Application.Abstractions;
using Users.Domain.Auth;

namespace Users.Application.Permissions.SetRolePermissionRules;

public class SetRolePermissionRuleCommandHandler(
    IUserDbContext context
    ) : IRequestHandler<SetRolePermissionRuleCommand, Result>
{
    private readonly IUserDbContext _context = context;

    public async Task<Result> Handle(SetRolePermissionRuleCommand request, CancellationToken cancellationToken)
    {
        await _context.PermissionRule
            .Where(x => x.RoleId == request.RoleId)
            .ExecuteDeleteAsync(cancellationToken: cancellationToken);
        var permissionRules = request.SetRolePermissionRuleIds.Select(x => new PermissionRule
        {
            PermissionId = x,
            UserId = null,
            RoleId = request.RoleId
        }).ToList();
        _context.PermissionRule.AddRange(permissionRules);
        await _context.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
