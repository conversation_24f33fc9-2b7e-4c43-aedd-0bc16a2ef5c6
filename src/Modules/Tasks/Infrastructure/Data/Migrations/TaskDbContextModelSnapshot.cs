﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Tasks.Infrastructure.Data;

#nullable disable

namespace Tasks.Infrastructure.Data.Migrations
{
    [DbContext(typeof(TaskDbContext))]
    partial class TaskDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("Tasks")
                .HasAnnotation("ProductVersion", "9.0.3")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Tasks.Domain.Comment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("History")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<DateTime>("InsertDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("InsertUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TaskId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdateDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdateUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TaskId");

                    b.ToTable("Comment", "Tasks");
                });

            modelBuilder.Entity("Tasks.Domain.Status", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ColorCode")
                        .IsRequired()
                        .HasMaxLength(7)
                        .HasColumnType("nvarchar(7)");

                    b.Property<bool>("IsClosed")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("Status", "Tasks");

                    b.HasData(
                        new
                        {
                            Id = new Guid("f8d5fa00-f81a-4d9e-9a0a-1e0f98ab2c5c"),
                            ColorCode = "#3498db",
                            IsClosed = false,
                            Name = "Yeni",
                            Order = 1
                        },
                        new
                        {
                            Id = new Guid("2f6b6e3d-6b2f-4f0f-8f2f-2b2f6b2f6b2f"),
                            ColorCode = "#f39c12",
                            IsClosed = false,
                            Name = "Devam Ediyor",
                            Order = 2
                        },
                        new
                        {
                            Id = new Guid("3f6b6e3d-6b2f-4f0f-8f2f-2b2f6b2f6b2f"),
                            ColorCode = "#2ecc71",
                            IsClosed = false,
                            Name = "Tamamlandı",
                            Order = 3
                        },
                        new
                        {
                            Id = new Guid("4f6b6e3d-6b2f-4f0f-8f2f-2b2f6b2f6b2f"),
                            ColorCode = "#e74c3c",
                            IsClosed = false,
                            Name = "İptal Edildi",
                            Order = 4
                        });
                });

            modelBuilder.Entity("Tasks.Domain.Task", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("History")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<DateTime>("InsertDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<Guid?>("InsertUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("NotificationWayId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Priority")
                        .HasColumnType("int");

                    b.Property<Guid>("ReporterUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("StatusId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("UpdateDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdateUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("StatusId");

                    b.ToTable("Task", "Tasks");
                });

            modelBuilder.Entity("Tasks.Domain.TaskDepartment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TaskId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TaskId", "DepartmentId")
                        .IsUnique();

                    b.ToTable("TaskDepartment", "Tasks");
                });

            modelBuilder.Entity("Tasks.Domain.Watchlist", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TaskId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TaskId", "UserId")
                        .IsUnique();

                    b.ToTable("Watchlist", "Tasks");
                });

            modelBuilder.Entity("Tasks.Domain.Comment", b =>
                {
                    b.HasOne("Tasks.Domain.Task", "Task")
                        .WithMany("Comments")
                        .HasForeignKey("TaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Task");
                });

            modelBuilder.Entity("Tasks.Domain.Task", b =>
                {
                    b.HasOne("Tasks.Domain.Status", "Status")
                        .WithMany("Tasks")
                        .HasForeignKey("StatusId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Status");
                });

            modelBuilder.Entity("Tasks.Domain.TaskDepartment", b =>
                {
                    b.HasOne("Tasks.Domain.Task", "Task")
                        .WithMany("TaskDepartments")
                        .HasForeignKey("TaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Task");
                });

            modelBuilder.Entity("Tasks.Domain.Watchlist", b =>
                {
                    b.HasOne("Tasks.Domain.Task", "Task")
                        .WithMany("Watchlist")
                        .HasForeignKey("TaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Task");
                });

            modelBuilder.Entity("Tasks.Domain.Status", b =>
                {
                    b.Navigation("Tasks");
                });

            modelBuilder.Entity("Tasks.Domain.Task", b =>
                {
                    b.Navigation("Comments");

                    b.Navigation("TaskDepartments");

                    b.Navigation("Watchlist");
                });
#pragma warning restore 612, 618
        }
    }
}
