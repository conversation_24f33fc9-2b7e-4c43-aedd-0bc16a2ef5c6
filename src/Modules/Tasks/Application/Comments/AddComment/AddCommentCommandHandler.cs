using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Tasks.Application.Abstractions;
using Tasks.Domain;

namespace Tasks.Application.Comments.AddComment;

public class AddCommentCommandHandler(
    ITaskDbContext dbContext
) : IRequestHandler<AddCommentCommand, Result<Guid>>
{
    private readonly ITaskDbContext _dbContext = dbContext;

    public async Task<Result<Guid>> Handle(AddCommentCommand request, CancellationToken cancellationToken)
    {
        var task = await _dbContext.Tasks.FirstOrDefaultAsync(t => t.Id == request.TaskId, cancellationToken);
        if (task == null)
        {
            return Result.Failure<Guid>("404", "Görev bulunamadı.");
        }
        var comment = new Comment
        {
            Id = Guid.NewGuid(),
            TaskId = request.TaskId,
            UserId = request.UserId,
            Content = request.Content,
            InsertDate = DateTime.UtcNow
        };
        _dbContext.Comments.Add(comment);
        task.Raise(new TaskCommentAddedEvent(task.Id, comment.Id, request.UserId));
        await _dbContext.SaveChangesAsync(cancellationToken);
        return Result.Success(comment.Id);
    }
}
