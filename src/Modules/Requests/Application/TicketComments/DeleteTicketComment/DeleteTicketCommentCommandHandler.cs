using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.TicketComments.DeleteTicketComment;

public class DeleteTicketCommentCommandHandler : IRequestHandler<DeleteTicketCommentCommand, Result>
{
    private readonly IRequestsDbContext _dbContext;
    private readonly IWorkContext _workContext;

    public DeleteTicketCommentCommandHandler(
        IRequestsDbContext dbContext,
        IWorkContext workContext)
    {
        _dbContext = dbContext;
        _workContext = workContext;
    }

    public async Task<Result> Handle(DeleteTicketCommentCommand request, CancellationToken cancellationToken)
    {
        var comment = await _dbContext.TicketComments
            .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken);

        if (comment == null)
        {
            return Result.Failure("TicketComment.NotFound", "Yorum bulunamadı.");
        }

        // Sadece kendi yorumunu veya admin yetkisi varsa silebilir
        if (comment.UserId != _workContext.UserId && !_workContext.HasRole("Admin"))
        {
            return Result.Failure("TicketComment.Unauthorized", "Bu yorumu silme yetkiniz yok.");
        }

        _dbContext.TicketComments.Remove(comment);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
