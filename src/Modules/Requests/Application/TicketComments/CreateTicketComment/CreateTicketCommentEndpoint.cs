using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Requests.Application.TicketComments.CreateTicketComment;

internal sealed class CreateTicketCommentEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/requests/tickets/{ticketId}/comments", async (
            Guid ticketId,
            CreateTicketCommentCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            // Ensure the ID in the route matches the ID in the command
            if (ticketId != command.TicketId)
            {
                command = command with { TicketId = ticketId };
            }

            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                id => Results.Created($"/api/v1/requests/tickets/{ticketId}/comments/{id}", id),
                CustomResults.Problem);
        })
        .WithTags("Requests.TicketComments")
        .WithGroupName("apiv1")
        .RequireAuthorization("Requests.TicketComments");
    }
}
