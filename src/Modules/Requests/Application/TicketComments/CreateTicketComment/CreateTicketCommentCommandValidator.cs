using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;

namespace Requests.Application.TicketComments.CreateTicketComment;

public class CreateTicketCommentCommandValidator : AbstractValidator<CreateTicketCommentCommand>
{
    private readonly IRequestsDbContext _dbContext;

    public CreateTicketCommentCommandValidator(IRequestsDbContext dbContext)
    {
        _dbContext = dbContext;

        RuleFor(x => x.TicketId)
            .NotEmpty().WithMessage("Ticket ID boş olamaz.")
            .MustAsync(BeExistingTicket).WithMessage("Belirtilen ticket bulunamadı.");

        RuleFor(x => x.Comment)
            .NotEmpty().WithMessage("Yorum boş olamaz.");
    }

    private async Task<bool> BeExistingTicket(Guid ticketId, CancellationToken cancellationToken)
    {
        return await _dbContext.Tickets
            .AnyAsync(t => t.Id == ticketId, cancellationToken);
    }
}
