using MediatR;
using Shared.Application;

namespace Requests.Application.TicketComments.ListTicketComments;

public record ListTicketCommentsQuery(
    Guid TicketId,
    int PageNumber = 1,
    int PageSize = 10
) : IRequest<PagedResult<TicketCommentDto>>;

public record TicketCommentDto
{
    public Guid Id { get; init; }
    public Guid TicketId { get; init; }
    public Guid UserId { get; init; }
    public string? UserName { get; init; }
    public string Comment { get; init; } = string.Empty;
    public DateTime InsertDate { get; init; }
}
