using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Requests.Application.TicketComments.ListTicketComments;

internal sealed class ListTicketCommentsEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/requests/tickets/{ticketId}/comments", async (
            Guid ticketId,
            [AsParameters] ListTicketCommentsQuery query,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            // Ensure the ID in the route matches the ID in the query
            if (ticketId != query.TicketId)
            {
                query = query with { TicketId = ticketId };
            }

            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Requests.TicketComments")
        .WithGroupName("apiv1")
        .RequireAuthorization("Requests.TicketComments");
    }
}
