using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.Tickets.UpdateTicket;

public class UpdateTicketCommandHandler(
    IRequestsDbContext dbContext
) : IRequestHandler<UpdateTicketCommand, Result>
{
    private readonly IRequestsDbContext _dbContext = dbContext;

    public async Task<Result> Handle(UpdateTicketCommand request, CancellationToken cancellationToken)
    {
        var ticket = await _dbContext.Tickets
            .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

        if (ticket == null)
        {
            return Result.Failure("Ticket.NotFound", "Ticket bulunamadı.");
        }

        // Ticket bilgilerini güncelle
        ticket.SubjectId = request.SubjectId;
        ticket.Title = request.Title;
        ticket.Description = request.Description;
        ticket.NotificationWayId = request.NotificationWayId;
        ticket.UserId = request.UserId;
        ticket.DepartmentIds = request.DepartmentIds;
        ticket.Priority = request.Priority;
        ticket.StatusId = Guid.Empty;
        ticket.EndDate = request.EndDate;
        ticket.Watchlist = request.Watchlist ?? [];

        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
