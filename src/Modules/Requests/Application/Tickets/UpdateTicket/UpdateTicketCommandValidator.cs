using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;

namespace Requests.Application.Tickets.UpdateTicket;

public class UpdateTicketCommandValidator : AbstractValidator<UpdateTicketCommand>
{
    private readonly IRequestsDbContext _dbContext;

    public UpdateTicketCommandValidator(IRequestsDbContext dbContext)
    {
        _dbContext = dbContext;

        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Ticket ID boş olamaz.")
            .MustAsync(BeExistingTicket).WithMessage("Belirtilen ticket bulunamadı.");

        RuleFor(x => x.SubjectId)
            .NotEmpty().WithMessage("Konu seçilmelidir.")
            .MustAsync(BeExistingSubject).WithMessage("Seçilen konu bulunamadı.");

        RuleFor(x => x.Title)
            .NotEmpty().WithMessage("Başlık boş olamaz.")
            .MaximumLength(200).WithMessage("Başlık 200 karakterden uzun olamaz.");

        RuleFor(x => x.Priority)
            .IsInEnum().WithMessage("Geçersiz öncelik değeri.");

    }

    private async Task<bool> BeExistingTicket(Guid id, CancellationToken cancellationToken)
    {
        return await _dbContext.Tickets
            .AnyAsync(t => t.Id == id, cancellationToken);
    }

    private async Task<bool> BeExistingSubject(Guid subjectId, CancellationToken cancellationToken)
    {
        return await _dbContext.TicketSubjects
            .AnyAsync(s => s.Id == subjectId, cancellationToken);
    }
}
