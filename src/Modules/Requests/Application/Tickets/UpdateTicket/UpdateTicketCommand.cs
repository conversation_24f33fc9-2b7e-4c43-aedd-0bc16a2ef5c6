using MediatR;
using Requests.Domain;
using Shared.Application;

namespace Requests.Application.Tickets.UpdateTicket;

public record UpdateTicketCommand : IRequest<Result>
{
    public Guid Id { get; init; }
    public Guid SubjectId { get; init; }
    public required string Title { get; init; }
    public string? Description { get; init; }
    public Guid? NotificationWayId { get; init; }
    public Guid? UserId { get; init; }
    public List<Guid> DepartmentIds { get; init; } = [];
    public PriorityEnum Priority { get; init; }
    public DateTime? EndDate { get; init; }
    public List<Guid>? Watchlist { get; init; }
}
