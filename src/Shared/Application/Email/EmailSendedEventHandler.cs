using MediatR;
using Shared.Domain;
using Shared.Infrastructure.External.Email;

namespace Shared.Application.Email;

internal sealed class EmailSendedEventHandler(
    IEmailManager emailManager
) : INotificationHandler<EmailSendedEvent>
{
    private readonly IEmailManager _emailManager = emailManager;

    public async Task Handle(EmailSendedEvent notification, CancellationToken cancellationToken)
    {
        await _emailManager.SendEmailAsync(notification.Email, notification.Title, notification.Body, isBodyHtml: true);
    }
}
