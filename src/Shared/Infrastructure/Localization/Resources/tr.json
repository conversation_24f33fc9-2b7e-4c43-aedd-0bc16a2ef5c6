{"DefaultRole": "<PERSON><PERSON>", "FileUpload": "<PERSON><PERSON><PERSON>", "FileUploadSuccess": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>ü<PERSON>.", "InvalidFileFormat": "Geçersiz dosya formatı.", "MissingRequiredField": "<PERSON><PERSON><PERSON><PERSON> alan <PERSON>.", "UnauthorizedAccess": "Bu işlemi gerçekleştirmek için yetkiniz yok.", "FileNotFound": "<PERSON><PERSON><PERSON> b<PERSON>.", "FileTooLarge": "<PERSON><PERSON><PERSON> boyutu <PERSON> büyü<PERSON>.", "InvalidFileType": "Geçersiz dosya türü.", "UploadFailed": "<PERSON><PERSON><PERSON> yü<PERSON>me başarısız oldu.", "FileAlreadyExists": "<PERSON><PERSON><PERSON> zaten mevcut.", "FileUploadInProgress": "<PERSON><PERSON><PERSON>.", "FileUploadCanceled": "<PERSON><PERSON><PERSON> iptal edildi.", "FileUploadCompleted": "<PERSON><PERSON><PERSON> ta<PERSON>.", "FileUploadFailed": "<PERSON><PERSON><PERSON> yü<PERSON>me başarısız oldu.", "FileUploadPaused": "<PERSON><PERSON><PERSON> y<PERSON> duraklatıldı.", "FileUploadResumed": "<PERSON><PERSON><PERSON> de<PERSON>m et<PERSON>.", "FileUploadRestarted": "<PERSON><PERSON><PERSON> yü<PERSON>me yeniden başlatıldı.", "FileUploadAborted": "<PERSON><PERSON><PERSON>.", "FileUploadTimeout": "<PERSON><PERSON><PERSON> yü<PERSON>me zaman aşımına uğradı.", "FileUploadError": "<PERSON><PERSON><PERSON> hatası.", "FileUploadNotSupported": "<PERSON><PERSON><PERSON>.", "FileUploadNotAllowed": "<PERSON><PERSON><PERSON> y<PERSON>meye izin verilmiyor.", "FileUploadLimitExceeded": "Dosya yükleme limiti aşıldı.", "FileUploadInvalid": "Geçersiz <PERSON>.", "FileUploadSuccessful": "<PERSON><PERSON><PERSON> başarılı.", "FileExtensionNotSupported": "Sadece {{Extensions}} uzantıları destekleniyor.", "FileSizeExceeded": "Yüklenen dosya {{MaxMb}}MB'ı geçemez.", "Users.Status.Active": "Aktif", "Users.Status.Inactive": "<PERSON><PERSON><PERSON>", "User.NotFound": "Kullanıcı bulunamadı.", "ExcelUserColumns.Name": "Ad", "ExcelUserColumns.Surname": "Soyad", "ExcelUserColumns.Email": "E-posta", "ExcelUserColumns.Phone": "Telefon", "ExcelUserColumns.Company": "Şirket", "ExcelUserColumns.Position": "Pozisyon", "ExcelUserColumns.Address": "<PERSON><PERSON>", "ExcelUserColumns.City": "Şehir", "ExcelUserColumns.ExtensionNo": "Dahili No", "ExcelUserColumns.Departman": "<PERSON><PERSON><PERSON>", "ExcelUserColumns.Group": "Grup", "ExcelUserColumns.Active": "Durum(0:<PERSON><PERSON><PERSON>, 1:<PERSON><PERSON><PERSON>)", "ExcelUserColumns.Password": "Şifre", "ExcelUserColumns.InsertDate": "<PERSON><PERSON><PERSON>", "ExcelReadError": "Excel dosyasından veri okunamadı.", "InvalidExcelData": "Excel dosyası, modele uygun veri içermiyor.", "NoNewUsersToImport": "İçe aktarılacak yeni kullanıcı bulunamadı.", "Row": "Satır", "UnexpectedError": "Beklenmedik bir hata <PERSON>.", "NameRequired": "<PERSON>sim alanı boş olamaz.", "SurnameRequired": "<PERSON><PERSON><PERSON><PERSON> alanı boş olamaz.", "EmailRequired": "E-posta alanı boş olamaz.", "EmailFormatInvalid": "Geçerli bir e-posta adresi giriniz.", "PhoneRequired": "Telefon alanı boş olamaz.", "ExtentionNoRequired": "<PERSON><PERSON><PERSON> numara alanı bo<PERSON> olamaz.", "ExtentionNoFormatInvalid": "<PERSON><PERSON><PERSON> numara yalnız<PERSON> rakam<PERSON>an oluşmalıdır.", "ExcelMappingError": "Satır {{Row}}: '{{Column}}' sütunundaki '{{Value}}' de<PERSON><PERSON>, '{{Property}}' <PERSON><PERSON><PERSON><PERSON>.", "Import.ValidationError": "{{Row}}. sat<PERSON><PERSON> hata: {{Detail}}", "Import.AlreadyExists": "Satır {{Row}}: '{{Email}}' ad<PERSON>i sistemde zaten mevcut.", "Import.ExtensionAlreadyExists": "Satır {{Row}}: '{{Extension}}' da<PERSON><PERSON> numa<PERSON> sistemde zaten mevcut.", "Import.ExtensionAlreadyPhoneExists": "Satır {{Row}}: '{{Em<PERSON>}}', '{{Phone}}' adresi sistemde zaten mevcut.", "Import.PropertyValidationError": "{{Row}}. satırda '{{Property}}' alan<PERSON> hatalı: {{Detail}}", "UserImport.ValidationFailed": "Kullanıcı içe aktarımı başarısız oldu. Lütfen geçerli veriler sağladığınızdan emin olun ve tekrar deneyin.", "UserImport.AlreadyExists": "Satır {{Row}}: '{{Email}}' ad<PERSON>i sistemde zaten mevcut.", "UserImport.ExtensionAlreadyExists": "Satır {{Row}}: '{{Extension}}' da<PERSON><PERSON> numa<PERSON> sistemde zaten mevcut.", "UserImport.ValidationError": "Satır {{Row}}: '{{Detail}}' hat<PERSON><PERSON>.", "ExcelCustomerColumns.Name": "Ad", "ExcelCustomerColumns.Surname": "Soyad", "ExcelCustomerColumns.Email": "E-posta", "ExcelCustomerColumns.Phone": "Telefon", "ExcelCustomerColumns.PhonePrefix": "<PERSON><PERSON><PERSON>", "ExcelUserColumns.PhonePrefix": "<PERSON><PERSON><PERSON>", "ExcelCustomerColumns.Type": "Müşteri Türü: Individual, Corporate", "ExcelCustomerColumns.Kind": "Müşteri Tipi: <PERSON>er, <PERSON>tentialCust<PERSON>, <PERSON>w", "ExcelCustomerColumns.Status": "Durum: Active, Inactive, Suspended", "ExcelCustomerColumns.Advisor": "<PERSON><PERSON><PERSON><PERSON>", "ExcelCustomerColumns.Classification": "Sınıflandırma", "ExcelCustomerColumns.Source": "<PERSON><PERSON><PERSON>", "ExcelCustomerColumns.Country": "<PERSON><PERSON><PERSON>", "ExcelCustomerColumns.TaxNumber": "Vergi No", "ExcelCustomerColumns.TaxOffice": "<PERSON><PERSON><PERSON>", "ExcelCustomerColumns.IdentificationNumber": "<PERSON><PERSON>", "ExcelCustomerColumns.Language": "Dil", "ExcelCustomerColumns.AvailableLanguage": "Mevcut Dil", "ExcelCustomerColumns.Description": "<PERSON><PERSON>ı<PERSON><PERSON>", "ExcelCustomerColumns.MailBcc": "Mail Bcc", "ExcelCustomerColumns.CustomerSource": "Müşteri Kaynağı", "ExcelCustomerColumns.NotificationWay": "<PERSON><PERSON><PERSON><PERSON>", "ExcelCustomerDto.NameEmpty": "Ad alanı boş olamaz.", "ExcelCustomerDto.NameMaxLength": "Ad alanı 100 karakterden uzun olamaz.", "ExcelCustomerDto.SurnameEmpty": "Soyad alanı boş olamaz.", "ExcelCustomerDto.SurnameMaxLength": "Soyad alanı 100 karakterden uzun olamaz.", "ExcelCustomerDto.EmailEmpty": "<PERSON><PERSON> al<PERSON> boş o<PERSON>az.", "ExcelCustomerDto.EmailInvalid": "Geçersiz email formatı.", "ExcelCustomerDto.PhoneEmpty": "Telefon alanı boş olamaz.", "ExcelCustomerDto.PhoneInvalid": "Geçersiz telefon numarası formatı.", "ExcelCustomerDto.TypeEmpty": "<PERSON><PERSON><PERSON> al<PERSON> boş o<PERSON>az.", "ExcelCustomerDto.TypeInvalid": "Geçersiz tür. Geçerli türler: Individual, Corporate.", "ExcelCustomerDto.StatusInvalid": "Geçersiz durum. Geçerli durumlar: Active, Inactive, Suspended.", "ExcelCustomerDto.KindInvalid": "Geçersiz tür. Geçerli türler: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>.", "ExcelCustomerDto.CountryMaxLength": "Ülke adı 100 karakterden uzun olamaz.", "ExcelCustomerDto.LanguageMaxLength": "Dil adı 50 karakterden uzun olamaz.", "ExcelCustomerDto.DescriptionMaxLength": "Açıklama 500 karakterden uzun olamaz.", "ExcelCustomerDto.TaxOfficeMaxLength": "<PERSON>ergi da<PERSON>i 100 karakterden uzun olamaz.", "ExcelCustomerDto.TaxNumberMaxLength": "Vergi numarası 50 karakterden uzun olamaz.", "ExcelCustomerDto.IdentificationNumberMaxLength": "Kimlik numarası 50 karakterden uzun olamaz.", "ExcelCustomerDto.MailBccMaxLength": "BCC alanı 100 karakterden uzun olamaz.", "ExcelCustomerDto.ClassificationMaxLength": "Sınıflandırma adı 100 karakterden uzun olamaz.", "ExcelCustomerDto.CustomerSourceMaxLength": "Müşteri kaynağı 100 karakterden uzun olamaz.", "ExcelCustomerDto.AdvisorEmailInvalid": "Geçersiz <PERSON>şman email adresi.", "ExcelCustomerDto.NotificationWayMaxLength": "Bildirim yöntemi 100 karakterden uzun olamaz.", "CustomerImport.ValidationFailed": "Müşteri içe aktarımı başarısız oldu. Lütfen geçerli veriler sağladığınızdan emin olun ve tekrar deneyin.", "CustomerImport.AlreadyExists": "Satır {{Row}}: '{{Email}}' ad<PERSON>i sistemde zaten mevcut.", "CustomerImport.ExtensionAlreadyPhoneExists": "Satır {{Row}}: '{{Em<PERSON>}}', '{{Phone}}' adresi sistemde zaten mevcut.", "CustomerImport.AdvisorNotFound": "{{Row}} n<PERSON><PERSON><PERSON> sat<PERSON>, {{AdvisorEmail}} email adresine sahip bir dan<PERSON><PERSON>man bulunamadı.", "CustomerImport.ValidationFailedDetail": "Lütfen aşağıdaki hataları düzelterek tekrar deneyiniz.", "Customers.NotFound": "Müşteri bulunamadı.", "TempCustomer.Validation.NameSurnameRequired": "Ad ve soyad zorunludur.", "TempCustomer.Validation.EmailInvalid": "Geçersiz e-posta formatı: {Email}", "TempCustomer.Validation.PhoneInvalid": "Telefon numarası yalnızca rakamlardan oluşmalıdır.", "Export.LimitExceeded": "En fazla 10.000 kayıt dışa aktarılabilir.", "Export.Success": "Dışa aktarma işlemi başarılı.", "EmailPart.EmptyFallback": "_bos", "UnknownUser": "Bilinmeyen Kullanıcı", "Customers.BulkDeleteCustomer.NoIdsProvided": "Silinecek müşteri kimlikleri sağlanmadı.", "Customers.BulkDeleteCustomer.Failed": "Müşteri silme işlemi başarısız oldu. Lütfen geçerli müşteri kimlikleri sağladığınızdan emin olun ve tekrar deneyin.", "Customers.BulkDeleteCustomer.Success": "Seçilen müşteriler baş<PERSON><PERSON><PERSON> si<PERSON>.", "Customers.BulkDeleteCustomer.SomeIdsNotFound": "Silinecek bazı müşteri kimlikleri bulunamadı.", "Customers.DeleteCustomer.Failed": "Müşteri silme işlemi başarısız oldu. Lütfen geçerli müşteri kimliği sağladığınızdan emin olun ve tekrar deneyin.", "Customers.DeleteTempCustomer.Failed": "Geçici müşteri silme işlemi başarısız oldu. Lütfen geçerli müşteri kimliği sağladığınızdan emin olun ve tekrar deneyin."}