{"DefaultRole": "General", "FileUpload": "File Upload", "FileUploadSuccess": "File uploaded successfully.", "InvalidFileFormat": "Invalid file format.", "MissingRequiredField": "Required field is missing.", "UnauthorizedAccess": "You are not authorized to perform this action.", "FileNotFound": "File not found.", "FileTooLarge": "File size is too large.", "InvalidFileType": "Invalid file type.", "UploadFailed": "File upload failed.", "FileAlreadyExists": "File already exists.", "FileUploadInProgress": "File upload in progress.", "FileUploadCanceled": "File upload canceled.", "FileUploadCompleted": "File upload completed.", "FileUploadFailed": "File upload failed.", "FileUploadPaused": "File upload paused.", "FileUploadResumed": "File upload resumed.", "FileUploadRestarted": "File upload restarted.", "FileUploadAborted": "File upload aborted.", "FileUploadTimeout": "File upload timed out.", "FileUploadError": "File upload error.", "FileUploadNotSupported": "File upload not supported.", "FileUploadNotAllowed": "File upload not allowed.", "FileUploadLimitExceeded": "File upload limit exceeded.", "FileUploadInvalid": "File upload invalid.", "FileUploadSuccessful": "File upload successful.", "FileExtensionNotSupported": "Only {{Extensions}} extensions are supported.", "FileSizeExceeded": "Uploaded file cannot exceed {{MaxMb}}MB.", "Users.Status.Active": "Active", "Users.Status.Inactive": "Inactive", "User.NotFound": "User not found.", "ExcelUserColumns.Name": "Name", "ExcelUserColumns.Surname": "Surname", "ExcelUserColumns.Email": "Email", "ExcelUserColumns.Phone": "Phone", "ExcelUserColumns.Company": "Company", "ExcelUserColumns.Position": "Position", "ExcelUserColumns.Address": "Address", "ExcelUserColumns.City": "City", "ExcelUserColumns.ExtensionNo": "Extension No", "ExcelUserColumns.Departman": "Department", "ExcelUserColumns.Group": "Group", "ExcelUserColumns.Active": "Status(0:Inactive, 1:Active)", "ExcelUserColumns.Password": "Password", "ExcelUserColumns.InsertDate": "Insert Date", "ExcelReadError": "Data could not be read from the Excel file.", "InvalidExcelData": "The Excel file does not contain data matching the model.", "NoNewUsersToImport": "No new users to import.", "Row": "Row", "UnexpectedError": "An unexpected error occurred.", "NameRequired": "Name field cannot be empty.", "SurnameRequired": "Surname field cannot be empty.", "EmailRequired": "Email field cannot be empty.", "EmailFormatInvalid": "Please enter a valid email address.", "PhoneRequired": "Phone field cannot be empty.", "ExtentionNoRequired": "Extension number field cannot be empty.", "ExtentionNoFormatInvalid": "Extension number must consist of digits only.", "ExcelMappingError": "Row {{Row}}: The value '{{Value}}' in the '{{Column}}' column could not be mapped to the '{{Property}}' field.", "Import.ValidationError": "Row {{Row}: {{Detail}}", "Import.AlreadyExists": "Row {{Row}}: The email '{{Email}}' already exists in the system.", "Import.ExtensionAlreadyExists": "Row {{Row}}: The extension is '{{Extension}}' already exists in the system.", "Import.ExtensionAlreadyPhoneExists": "Row {{Row}}: '{{<PERSON><PERSON>}}', '{{Phone}}' already exists in the system.", "Import.PropertyValidationError": "Row {{Row}}: Field '{{Property}}' is invalid - {{Detail}}", "UserImport.ValidationFailed": "User import failed. Please ensure valid data is provided and try again.", "UserImport.AlreadyExists": "Row {{Row}}: The email '{{Email}}' already exists in the system.", "UserImport.ExtensionAlreadyExists": "Row {{Row}}: The extension '{{Extension}}' already exists in the system.", "UserImport.ValidationError": "Row {{Row}}: The following error occurred - '{{Detail}}'", "ExcelCustomerColumns.Name": "Name", "ExcelCustomerColumns.Surname": "Surname", "ExcelCustomerColumns.Email": "Email", "ExcelCustomerColumns.Phone": "Phone", "ExcelCustomerColumns.PhonePrefix": "Phone Prefix", "ExcelUserColumns.PhonePrefix": "Phone Prefix", "ExcelCustomerColumns.Type": "Customer Type: Individual, Corporate", "ExcelCustomerColumns.Kind": "Customer Type: Customer, PotentialCustomer, Renew", "ExcelCustomerColumns.Status": "Status: Active, Inactive, Suspended", "ExcelCustomerColumns.Advisor": "Advisor", "ExcelCustomerColumns.Classification": "Classification", "ExcelCustomerColumns.Source": "Source", "ExcelCustomerColumns.Country": "Country", "ExcelCustomerColumns.TaxNumber": "Tax Number", "ExcelCustomerColumns.TaxOffice": "Tax Office", "ExcelCustomerColumns.IdentificationNumber": "Identification Number", "ExcelCustomerColumns.Language": "Language", "ExcelCustomerColumns.AvailableLanguage": "Available Language", "ExcelCustomerColumns.Description": "Description", "ExcelCustomerColumns.MailBcc": "Mail BCC", "ExcelCustomerColumns.CustomerSource": "Customer Source", "ExcelCustomerColumns.NotificationWay": "Notification Way", "ExcelCustomerDto.NameEmpty": "Name field cannot be empty.", "ExcelCustomerDto.NameMaxLength": "Name field cannot exceed 100 characters.", "ExcelCustomerDto.SurnameEmpty": "Surname field cannot be empty.", "ExcelCustomerDto.SurnameMaxLength": "Surname field cannot exceed 100 characters.", "ExcelCustomerDto.EmailEmpty": "Email field cannot be empty.", "ExcelCustomerDto.EmailInvalid": "Invalid email format.", "ExcelCustomerDto.PhoneEmpty": "Phone field cannot be empty.", "ExcelCustomerDto.PhoneInvalid": "Invalid phone number format.", "ExcelCustomerDto.TypeEmpty": "Type field cannot be empty.", "ExcelCustomerDto.TypeInvalid": "Invalid type. Valid types: Individual, Corporate.", "ExcelCustomerDto.StatusInvalid": "Invalid status. Valid statuses: Active, Inactive, Suspended.", "ExcelCustomerDto.KindInvalid": "Invalid kind. Valid kinds: Customer, PotentialCustomer, Renew.", "ExcelCustomerDto.CountryMaxLength": "Country name cannot exceed 100 characters.", "ExcelCustomerDto.LanguageMaxLength": "Language name cannot exceed 50 characters.", "ExcelCustomerDto.DescriptionMaxLength": "Description cannot exceed 500 characters.", "ExcelCustomerDto.TaxOfficeMaxLength": "Tax office name cannot exceed 100 characters.", "ExcelCustomerDto.TaxNumberMaxLength": "Tax number cannot exceed 50 characters.", "ExcelCustomerDto.IdentificationNumberMaxLength": "Identification number cannot exceed 50 characters.", "ExcelCustomerDto.MailBccMaxLength": "BCC field cannot exceed 100 characters.", "ExcelCustomerDto.ClassificationMaxLength": "Classification name cannot exceed 100 characters.", "ExcelCustomerDto.CustomerSourceMaxLength": "Customer source cannot exceed 100 characters.", "ExcelCustomerDto.AdvisorEmailInvalid": "Invalid advisor email address.", "ExcelCustomerDto.NotificationWayMaxLength": "Notification way cannot exceed 100 characters.", "CustomerImport.ValidationFailed": "Customer import failed. Please ensure valid data is provided and try again.", "CustomerImport.AlreadyExists": "Row {{Row}}: The email '{{Email}}' already exists in the system.", "CustomerImport.ExtensionAlreadyPhoneExists": "Row {{Row}}: '{{<PERSON><PERSON>}}', '{{Phone}}' already exists in the system.", "CustomerImport.AdvisorNotFound": "Row {{Row}}: No advisor found with the email address '{{AdvisorEmail}}'.", "CustomerImport.ValidationFailedDetail": "Please correct the errors below and try again.", "Customers.NotFound": "Customer not found.", "TempCustomer.Validation.NameSurnameRequired": "Ad ve soyad zorunludur.", "TempCustomer.Validation.EmailInvalid": "Geçersiz e-posta formatı: {Email}", "TempCustomer.Validation.PhoneInvalid": "Telefon numarası yalnızca rakamlardan oluşmalıdır.", "Export.LimitExceeded": "A maximum of 10,000 records can be exported.", "Export.Success": "Export operation was successful.", "EmailPart.EmptyFallback": "_empty", "UnknownUser": "Unknown User", "Customers.BulkDeleteCustomer.NoIdsProvided": "No customer IDs were provided for deletion.", "Customers.BulkDeleteCustomer.Failed": "Customer deletion failed. Please ensure valid customer IDs are provided and try again.", "Customers.BulkDeleteCustomer.Success": "Customer deletion successful.", "Customers.BulkDeleteCustomer.SomeIdsNotFound": "Some customer IDs provided for deletion were not found.", "Customers.DeleteCustomer.Failed": "Customer deletion failed. Please ensure a valid customer ID is provided and try again.", "Customers.DeleteTempCustomer.Failed": "Temporary customer deletion failed. Please ensure a valid customer ID is provided and try again."}